import { Button, Container, Input, Label, Text, toast } from "@medusajs/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { useUpdateCompany } from "../../../../hooks/api/companies"
import { Company, AdminUpdateCompany } from "../../../../types"

interface CompanyFormData extends AdminUpdateCompany {}

interface CompanyEditFormProps {
  company: Company
}

export const CompanyEditForm = ({ company }: CompanyEditFormProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CompanyFormData>({
    defaultValues: {
      name: company.name,
      email: company.email,
      phone: company.phone || "",
      website: company.website || "",
      address: company.address,
      city: company.city,
      state: company.state,
      zip: company.zip,
      description: company.description || "",
    }
  })

  const updateCompanyMutation = useUpdateCompany(company.id, {
    onSuccess: () => {
      toast.success(t("companies.toasts.updateSuccess", "Successfully updated company"))
      navigate(`/companies/${company.id}`)
    },
    onError: (error) => {
      toast.error(t("companies.toasts.updateError", "Failed to update company"))
    },
  })

  const onSubmit = (data: CompanyFormData) => {
    updateCompanyMutation.mutate(data)
  }

  return (
    <Container className="p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">
              {t("companies.fields.name", "Company Name")} *
            </Label>
            <Input
              id="name"
              {...register("name", { 
                required: t("companies.validation.nameRequired", "Company name is required") 
              })}
              placeholder={t("companies.placeholders.name", "Enter company name")}
            />
            {errors.name && (
              <Text className="text-ui-fg-error text-sm mt-1">
                {errors.name.message}
              </Text>
            )}
          </div>

          <div>
            <Label htmlFor="email">
              {t("companies.fields.email", "Email")} *
            </Label>
            <Input
              id="email"
              type="email"
              {...register("email", { 
                required: t("companies.validation.emailRequired", "Email is required"),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: t("companies.validation.emailInvalid", "Invalid email address")
                }
              })}
              placeholder={t("companies.placeholders.email", "Enter email address")}
            />
            {errors.email && (
              <Text className="text-ui-fg-error text-sm mt-1">
                {errors.email.message}
              </Text>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="phone">
              {t("companies.fields.phone", "Phone")}
            </Label>
            <Input
              id="phone"
              {...register("phone")}
              placeholder={t("companies.placeholders.phone", "Enter phone number")}
            />
          </div>

          <div>
            <Label htmlFor="website">
              {t("companies.fields.website", "Website")}
            </Label>
            <Input
              id="website"
              {...register("website")}
              placeholder={t("companies.placeholders.website", "Enter website URL")}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="address">
            {t("companies.fields.address", "Address")} *
          </Label>
          <Input
            id="address"
            {...register("address", { 
              required: t("companies.validation.addressRequired", "Address is required") 
            })}
            placeholder={t("companies.placeholders.address", "Enter street address")}
          />
          {errors.address && (
            <Text className="text-ui-fg-error text-sm mt-1">
              {errors.address.message}
            </Text>
          )}
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="city">
              {t("companies.fields.city", "City")} *
            </Label>
            <Input
              id="city"
              {...register("city", { 
                required: t("companies.validation.cityRequired", "City is required") 
              })}
              placeholder={t("companies.placeholders.city", "Enter city")}
            />
            {errors.city && (
              <Text className="text-ui-fg-error text-sm mt-1">
                {errors.city.message}
              </Text>
            )}
          </div>

          <div>
            <Label htmlFor="state">
              {t("companies.fields.state", "State")} *
            </Label>
            <Input
              id="state"
              {...register("state", { 
                required: t("companies.validation.stateRequired", "State is required") 
              })}
              placeholder={t("companies.placeholders.state", "Enter state")}
            />
            {errors.state && (
              <Text className="text-ui-fg-error text-sm mt-1">
                {errors.state.message}
              </Text>
            )}
          </div>

          <div>
            <Label htmlFor="zip">
              {t("companies.fields.zip", "ZIP Code")} *
            </Label>
            <Input
              id="zip"
              {...register("zip", { 
                required: t("companies.validation.zipRequired", "ZIP code is required") 
              })}
              placeholder={t("companies.placeholders.zip", "Enter ZIP code")}
            />
            {errors.zip && (
              <Text className="text-ui-fg-error text-sm mt-1">
                {errors.zip.message}
              </Text>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="description">
            {t("companies.fields.description", "Description")}
          </Label>
          <Input
            id="description"
            {...register("description")}
            placeholder={t("companies.placeholders.description", "Enter company description")}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button 
            type="button" 
            variant="secondary"
            onClick={() => navigate(`/companies/${company.id}`)}
          >
            {t("general.cancel", "Cancel")}
          </Button>
          <Button 
            type="submit" 
            variant="primary"
            isLoading={updateCompanyMutation.isPending}
          >
            {t("companies.edit.submit", "Update Company")}
          </Button>
        </div>
      </form>
    </Container>
  )
}