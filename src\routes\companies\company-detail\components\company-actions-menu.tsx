import { <PERSON>, LockClosedSolid, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "@medusajs/icons"
import { toast, usePrompt } from "@medusajs/ui"
import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { ActionMenu } from "../../../../components/common/action-menu"
import { useDeleteCompany } from "../../../../hooks/api/companies"
import { useCustomerGroups } from "../../../../hooks/api/customer-groups"
import { Company } from "../../../../types"
import {
  CompanyUpdateDrawer,
  CompanyCustomerGroupDrawer,
  CompanyApprovalSettingsDrawer
} from "./drawers"

interface CompanyActionsMenuProps {
  company: Company
}

export const CompanyActionsMenu = ({ company }: CompanyActionsMenuProps) => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const navigate = useNavigate()

  const [editOpen, setEditOpen] = useState(false)
  const [customerGroupOpen, setCustomerGroupOpen] = useState(false)
  const [approvalSettingsOpen, setApprovalSettingsOpen] = useState(false)

  const { mutateAsync: mutateDelete, isPending: loadingDelete } = useDeleteCompany(company.id)
  const { customer_groups } = useCustomerGroups({ limit: 50 })

  const handleDelete = async () => {
    const result = await prompt({
      title: t("companies.delete.title", "Delete Company"),
      description: t("companies.delete.description", `Are you sure you want to delete company "${company.name}"? This action cannot be undone.`),
      confirmText: t("actions.delete", "Delete"),
      cancelText: t("actions.cancel", "Cancel"),
    })

    if (!result) {
      return
    }

    await mutateDelete(undefined, {
      onSuccess: () => {
        navigate("/companies")
        toast.success(t("companies.toasts.deleteSuccess", "Company deleted successfully"))
      },
      onError: () => {
        toast.error(t("companies.toasts.deleteError", "Failed to delete company"))
      }
    })
  }

  return (
    <>
      <ActionMenu
        groups={[
          {
            actions: [
              {
                icon: <PencilSquare />,
                label: t("companies.actions.editDetails", "Edit Details"),
                onClick: () => setEditOpen(true),
              },
              {
                icon: <Link />,
                label: t("companies.actions.manageCustomerGroup", "Manage Customer Group"),
                onClick: () => setCustomerGroupOpen(true),
              },
              {
                icon: <LockClosedSolid />,
                label: t("companies.actions.approvalSettings", "Approval Settings"),
                onClick: () => setApprovalSettingsOpen(true),
              },
            ],
          },
          {
            actions: [
              {
                icon: <Trash />,
                label: t("actions.delete", "Delete"),
                onClick: handleDelete,
              },
            ],
          },
        ]}
      />

      <CompanyUpdateDrawer
        company={company}
        open={editOpen}
        setOpen={setEditOpen}
      />
      <CompanyCustomerGroupDrawer
        company={company}
        customerGroups={customer_groups}
        open={customerGroupOpen}
        setOpen={setCustomerGroupOpen}
      />
      <CompanyApprovalSettingsDrawer
        company={company}
        open={approvalSettingsOpen}
        setOpen={setApprovalSettingsOpen}
      />

    </>
  )
}
