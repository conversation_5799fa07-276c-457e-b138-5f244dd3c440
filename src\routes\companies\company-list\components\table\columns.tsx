import { createColumnHelper } from "@tanstack/react-table"
import { Avatar, Badge, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Company } from "../../../../../types"
import { CompanyActionsMenu } from "../company-actions-menu"

const columnHelper = createColumnHelper<Company>()

export const useCompaniesTableColumns = () => {
  const { t } = useTranslation()

  return [
    columnHelper.display({
      id: "avatar",
      header: "",
      size: 24,
      minSize: 24,
      maxSize: 24,
      cell: ({ row }) => {
        const company = row.original
        return (
          <div className="w-6 h-6 flex items-center justify-center">
            <Avatar
              src={company.logo_url || undefined}
              fallback={company.name.charAt(0)}
              className="h-6 w-6"
            />
          </div>
        )
      },
    }),
    columnHelper.accessor("name", {
      header: t("companies.table.name", "公司名称"),
      enableSorting: true,
      cell: ({ getValue }) => getValue(),
    }),
    columnHelper.accessor("email", {
      header: t("companies.table.email", "Email"),
      enableSorting: true,
      cell: ({ getValue }) => <Text>{getValue()}</Text>,
    }),
    columnHelper.accessor("phone", {
      header: t("companies.table.phone", "Phone"),
      enableSorting: true,
      cell: ({ getValue }) => <Text>{getValue() || "-"}</Text>,
    }),
    columnHelper.display({
      id: "address",
      header: t("companies.table.address", "Address"),
      cell: ({ row }) => {
        const company = row.original
        return (
          <Text className="text-sm">
            {`${company.address}, ${company.city}, ${company.state} ${company.zip}`}
          </Text>
        )
      },
    }),
    columnHelper.display({
      id: "employees",
      header: t("companies.table.employees", "Employees"),
      cell: ({ row }) => {
        const company = row.original
        return <Text>{company.employees?.length || 0}</Text>
      },
    }),
    columnHelper.display({
      id: "customer_group",
      header: t("companies.table.customerGroup", "Customer Group"),
      cell: ({ row }) => {
        const company = row.original
        return company.customer_group?.name ? (
          <Badge size="small" color="blue">
            {company.customer_group.name}
          </Badge>
        ) : (
          <Text className="text-ui-fg-muted">-</Text>
        )
      },
    }),

    columnHelper.display({
      id: "actions",
      header: t("companies.table.actions", "Actions"),
      cell: ({ row }) => {
        const company = row.original
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <CompanyActionsMenu company={company} />
          </div>
        )
      },
    }),
  ]
}
