import { UIMatch } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { AdminQuoteResponse } from "../../../types"

type QuoteDetailBreadcrumbProps = UIMatch<AdminQuoteResponse>

export const Breadcrumb = (props: QuoteDetailBreadcrumbProps) => {
  const { t } = useTranslation()
  
  const quote = props.data?.quote
  
  if (!quote) {
    return t("quotes.title", "Quotes")
  }

  const displayNumber = quote.draft_order?.display_id || (() => {
    // 从ID中提取数字生成简单序号（备用方案）
    const numericPart = quote.id.replace(/\D/g, '')
    const lastDigits = numericPart.slice(-6) || '1'
    return parseInt(lastDigits, 10) % 10000 || 1
  })()
  
  return `${t("quotes.title", "Quotes")} / #${displayNumber}`
}