import { createColumnHelper } from "@tanstack/react-table"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { Quote } from "../../../../../types"
import { QuoteStatusBadge } from "../../../../../components/quotes/quote-status-badge"
import { DateCell } from "../../../../../components/table/table-cells/common/date-cell"
import { TextCell } from "../../../../../components/table/table-cells/common/text-cell"
import { MoneyAmountCell } from "../../../../../components/table/table-cells/common/money-amount-cell"

// 自定义日期时间显示组件
const DateTimeCell = ({ date }: { date: string }) => {
  if (!date) return <TextCell text="-" />

  const dateObj = new Date(date)

  // 格式化为 YYYY-MM-DD HH:mm:ss 格式
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')

  const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

  return <TextCell text={formatted} />
}

const columnHelper = createColumnHelper<Quote>()

export const useQuotesTableColumns = () => {
  const { t } = useTranslation()

  return useMemo(
    () => [
      columnHelper.accessor("id", {
        header: t("quotes.table.id", "Quote ID"),
        cell: ({ row }) => {
          const quote = row.original
          const displayNumber = quote.draft_order?.display_id || (() => {
            // 从ID中提取数字生成简单序号（备用方案）
            const numericPart = quote.id.replace(/\D/g, '')
            const lastDigits = numericPart.slice(-6) || '1'
            return parseInt(lastDigits, 10) % 10000 || 1
          })()
          return (
            <span className="font-mono text-sm">
              #{displayNumber}
            </span>
          )
        },
      }),
      columnHelper.accessor((row) => row.customer?.email, {
        id: "customer.email",
        header: t("quotes.table.customer", "Customer"),
        cell: ({ getValue }) => <TextCell text={getValue() || '-'} />,
      }),
      columnHelper.accessor((row) => row.customer?.company_name, {
        id: "customer.company_name",
        header: t("quotes.table.company", "Company"),
        cell: ({ getValue }) => <TextCell text={getValue() || '-'} />,
      }),
      columnHelper.accessor((row) => row.draft_order?.total, {
        id: "draft_order.total",
        header: t("quotes.table.amount", "Amount"),
        cell: ({ getValue, row }) => {
          const amount = getValue()
          const currencyCode = row.original.draft_order?.currency_code
          return amount && currencyCode ? (
            <MoneyAmountCell
              currencyCode={currencyCode}
              amount={amount}
            />
          ) : (
            <TextCell text="-" />
          )
        },
      }),
      columnHelper.accessor("status", {
        header: t("quotes.table.status", "Status"),
        cell: ({ getValue }) => <QuoteStatusBadge status={getValue()} />,
      }),
      columnHelper.accessor("created_at", {
        header: t("quotes.table.createdAt", "Created At"),
        cell: ({ getValue }) => <DateTimeCell date={getValue()} />,
      }),
      columnHelper.accessor("updated_at", {
        header: t("quotes.table.updatedAt", "Updated At"),
        cell: ({ getValue }) => <DateTimeCell date={getValue()} />,
      }),
    ],
    [t]
  )
}