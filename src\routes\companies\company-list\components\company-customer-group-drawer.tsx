import { <PERSON><PERSON>, <PERSON>er, Hint, Table, toast } from "@medusajs/ui"
import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useAddCompanyToCustomerGroup, useRemoveCompanyFromCustomerGroup } from "../../../../hooks/api/companies"
import { useCustomerGroups } from "../../../../hooks/api/customer-groups"
import { Company } from "../../../../types"
import { HttpTypes } from "@medusajs/types"

interface CompanyCustomerGroupDrawerProps {
  company: Company
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyCustomerGroupDrawer = ({ company, open, setOpen }: CompanyCustomerGroupDrawerProps) => {
  const { t } = useTranslation()
  
  const { customer_groups, isLoading } = useCustomerGroups({
    limit: 50,
  })

  const { mutateAsync: addMutate, isPending: addLoading } = useAddCompanyToCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success(t("companies.customerGroup.addSuccess", "Successfully added company to customer group"))
      setOpen(false)
    },
    onError: (error) => {
      toast.error(t("companies.customerGroup.addError", "Failed to add company to customer group"))
    },
  })

  const { mutateAsync: removeMutate, isPending: removeLoading } = useRemoveCompanyFromCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success(t("companies.customerGroup.removeSuccess", "Successfully removed company from customer group"))
    },
    onError: (error) => {
      toast.error(t("companies.customerGroup.removeError", "Failed to remove company from customer group"))
    },
  })

  const handleAdd = async (groupId: string) => {
    await addMutate(groupId)
  }

  const handleRemove = async (groupId: string) => {
    await removeMutate(groupId)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>{t("companies.customerGroup.title", "Manage Customer Group")}</Drawer.Title>
        </Drawer.Header>
        
        <Drawer.Body className="space-y-4 h-full overflow-y-hidden">
          <Hint variant="info">
            {t("companies.customerGroup.hint", "Assign this company to a customer group to apply group-specific pricing and permissions.")}
          </Hint>
          
          <div className="h-full overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-ui-fg-muted">{t("general.loading", "Loading...")}</div>
              </div>
            ) : (
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell>{t("companies.customerGroup.groupName", "Customer Group")}</Table.HeaderCell>
                    <Table.HeaderCell className="text-right">
                      {t("companies.customerGroup.actions", "Actions")}
                    </Table.HeaderCell>
                  </Table.Row>
                </Table.Header>

                <Table.Body>
                  {customer_groups?.map((group) => (
                    <Table.Row key={group.id}>
                      <Table.Cell>{group.name}</Table.Cell>
                      <Table.Cell className="text-right">
                        {company.customer_group?.id === group.id ? (
                          <Button
                            onClick={() => handleRemove(group.id)}
                            isLoading={removeLoading}
                            variant="danger"
                            size="small"
                          >
                            {t("companies.customerGroup.remove", "Remove")}
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleAdd(group.id)}
                            disabled={!!company.customer_group?.id || addLoading}
                            isLoading={addLoading}
                            size="small"
                          >
                            {t("companies.customerGroup.add", "Add")}
                          </Button>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            )}
          </div>
        </Drawer.Body>
      </Drawer.Content>
    </Drawer>
  )
}
