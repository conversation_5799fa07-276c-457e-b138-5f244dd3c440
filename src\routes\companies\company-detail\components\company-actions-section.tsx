import { But<PERSON>, Container, Heading, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { Pencil, Trash } from "@medusajs/icons"
import { useDeleteCompany } from "../../../../hooks/api/companies"
import { Company } from "../../../../types"

interface CompanyActionsSectionProps {
  company: Company
}

export const CompanyActionsSection = ({ company }: CompanyActionsSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [isDeleting, setIsDeleting] = useState(false)

  const deleteCompanyMutation = useDeleteCompany(company.id, {
    onSuccess: () => {
      toast.success(t("companies.toasts.deleteSuccess", "Successfully deleted company"))
      navigate("/companies")
    },
    onError: (error) => {
      toast.error(t("companies.toasts.deleteError", "Failed to delete company"))
      setIsDeleting(false)
    },
  })

  const handleDelete = () => {
    if (isDeleting) {
      deleteCompanyMutation.mutate()
    } else {
      setIsDeleting(true)
    }
  }

  return (
    <Container className="p-6">
      <Heading level="h2" className="mb-4">
        {t("companies.details.actions", "Actions")}
      </Heading>
      
      <div className="flex flex-col gap-2">
        <Button
          variant="secondary"
          onClick={() => navigate(`/companies/${company.id}/edit`)}
        >
          <Pencil className="h-4 w-4" />
          {t("companies.actions.edit", "Edit Company")}
        </Button>
        
        <Button
          variant={isDeleting ? "danger" : "secondary"}
          onClick={handleDelete}
          isLoading={deleteCompanyMutation.isPending}
        >
          <Trash className="h-4 w-4" />
          {isDeleting 
            ? t("companies.actions.confirmDelete", "Confirm Delete")
            : t("companies.actions.delete", "Delete Company")
          }
        </Button>

        {isDeleting && (
          <Button
            variant="secondary"
            size="small"
            onClick={() => setIsDeleting(false)}
          >
            {t("general.cancel", "Cancel")}
          </Button>
        )}
      </div>
    </Container>
  )
}