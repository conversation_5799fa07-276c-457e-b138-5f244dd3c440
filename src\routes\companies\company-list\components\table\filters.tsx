import { useTranslation } from "react-i18next"
import { useDataTableDateFilters } from "../../../../../components/data-table/helpers/general/use-data-table-date-filters"
import { useCustomerGroups } from "../../../../../hooks/api/customer-groups"

export const useCompaniesTableFilters = () => {
  const { t } = useTranslation()
  const dateFilters = useDataTableDateFilters()

  const { customer_groups } = useCustomerGroups({
    limit: 1000,
  })

  const customerGroupFilter = customer_groups ? {
    key: "customer_group",
    label: t("companies.table.customerGroup", "客户群组"),
    type: "select" as const,
    multiple: false,
    options: [
      { label: t("fields.all", "全部"), value: "" },
      ...customer_groups.map((group) => ({
        label: group.name,
        value: group.id,
      })),
    ],
  } : null

  return [
    ...(customerGroupFilter ? [customerGroupFilter] : []),
    ...dateFilters,
  ]
}
