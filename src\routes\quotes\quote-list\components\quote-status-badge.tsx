import { Badge } from "@medusajs/ui"
import { useTranslation } from "react-i18next"

interface QuoteStatusBadgeProps {
  status: string
}

export const QuoteStatusBadge = ({ status }: QuoteStatusBadgeProps) => {
  const { t } = useTranslation()

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "grey"
      case "pending":
        return "orange"
      case "sent":
        return "blue"
      case "accepted":
        return "green"
      case "rejected":
        return "red"
      case "expired":
        return "grey"
      default:
        return "grey"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "draft":
        return t("quotes.status.draft", "Draft")
      case "pending":
        return t("quotes.status.pending", "Pending")
      case "sent":
        return t("quotes.status.sent", "Sent")
      case "accepted":
        return t("quotes.status.accepted", "Accepted")
      case "rejected":
        return t("quotes.status.rejected", "Rejected")
      case "expired":
        return t("quotes.status.expired", "Expired")
      default:
        return status
    }
  }

  return (
    <Badge color={getStatusColor(status)} size="2xsmall">
      {getStatusLabel(status)}
    </Badge>
  )
}