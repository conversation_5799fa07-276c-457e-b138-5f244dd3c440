{"$schema": "./$schema.json", "general": {"ascending": "По зростанню", "descending": "По спаданню", "add": "Додати", "start": "Почати", "end": "Кінець", "open": "Відкрити", "close": "Закрити", "apply": "Застосувати", "range": "Діапазон", "search": "По<PERSON><PERSON>к", "of": "з", "results": "резуль<PERSON><PERSON><PERSON><PERSON>в", "pages": "сторінки", "next": "Наступна", "prev": "Попередня", "is": "є", "timeline": "Тай<PERSON><PERSON><PERSON><PERSON>н", "success": "Успіх", "warning": "Попередження", "tip": "Порада", "error": "Помилка", "select": "Вибрати", "selected": "Вибрано", "enabled": "Увімкнено", "disabled": "Вимкнено", "expired": "Закінчено", "active": "Активний", "revoked": "Скасовано", "new": "Новий", "modified": "Змінено", "added": "Додано", "removed": "Вилучено", "admin": "Адміністратор", "store": "Мага<PERSON>ин", "details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "items_one": "{{count}} товар", "items_other": "{{count}} товари", "countSelected": "{{count}} вибрано", "countOfTotalSelected": "{{count}} з {{total}} вибрано", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} ще", "areYouSure": "Ви впевнені?", "noRecordsFound": "Записів не знайдено", "typeToConfirm": "Будь ласка, введіть {val} для підтвердження:", "noResultsTitle": "Немає результатів", "noResultsMessage": "Спробуйте змінити фільтри або запит пошуку", "noSearchResults": "Результатів не знайдено", "noSearchResultsFor": "Немає результатів для <0>'{{query}}'</0>", "noRecordsTitle": "Немає записів", "noRecordsMessage": "Немає записів для відображення", "unsavedChangesTitle": "Ви впевнені, що хочете залишити цю форму?", "unsavedChangesDescription": "У вас є незбережені зміни, які будуть втрачені, якщо ви вийдете з форми.", "includesTaxTooltip": "Ціни в цій колонці включають податки.", "excludesTaxTooltip": "Ціни в цій колонці без податків.", "noMoreData": "Немає більше даних"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} клю<PERSON><PERSON>в", "drawer": {"header_one": "JSON <0>· {{count}} ключ</0>", "header_other": "JSON <0>· {{count}} ключів</0>", "description": "Перегляньте JSON дані для цього об'єкта."}}, "metadata": {"header": "Метадані", "numberOfKeys_one": "{{count}} кл<PERSON>ч", "numberOfKeys_other": "{{count}} клю<PERSON><PERSON>в", "edit": {"header": "Редагувати метадані", "description": "Редагуйте метадані для цього об'єкта.", "successToast": "Метадані успішно оновлено.", "actions": {"insertRowAbove": "Вставити рядок зверху", "insertRowBelow": "Вставити рядок знизу", "deleteRow": "Видалити рядок"}, "labels": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Значення"}, "complexRow": {"label": "Деякі рядки вимкнені", "description": "Цей об'єкт містить нефіксовані метадані, такі як масиви або об'єкти, які не можна редагувати тут. Щоб редагувати вимкнені рядки, використовуйте API безпосередньо.", "tooltip": "Цей рядок вимкнений, оскільки містить нефіксовані дані."}}}, "validation": {"mustBeInt": "Значення повинно бути цілим числом.", "mustBePositive": "Значення повинно бути додатнім числом."}, "actions": {"save": "Зберегти", "saveAsDraft": "Зберегти як чернетку", "copy": "Копіювати", "copied": "Скопійовано", "duplicate": "Дублювати", "publish": "Опублікувати", "create": "Створити", "delete": "Видалити", "remove": "Видалити", "revoke": "Скасувати", "cancel": "Скасувати", "forceConfirm": "Примусово підтвердити", "continueEdit": "Продовжити редагування", "enable": "Увімкнути", "disable": "Вимкнути", "undo": "Скасувати", "complete": "Завершити", "viewDetails": "Переглянути деталі", "back": "Назад", "close": "Закрити", "showMore": "Показати більше", "continue": "Продовжити", "continueWithEmail": "Продовжити з електронною поштою", "idCopiedToClipboard": "ID скопійовано в буфер обміну", "addReason": "Додати причину", "addNote": "Додати примітку", "reset": "Скинути", "confirm": "Підтвердити", "edit": "Редагувати", "addItems": "Додати елементи", "download": "Завант<PERSON><PERSON><PERSON>ти", "clear": "Очистити", "clearAll": "Очистити все", "apply": "Застосувати", "add": "Додати", "select": "Вибрати", "browse": "Переглянути", "logout": "Вийти", "hide": "Приховати", "export": "Експортувати", "import": "Імпортувати", "cannotUndo": "Цю дію не можна скасувати"}, "operators": {"in": "У"}, "app": {"search": {"label": "По<PERSON><PERSON>к", "title": "По<PERSON><PERSON>к", "description": "Пошук по всьому магазину, включаючи замовлення, продукти, клієнтів і більше.", "allAreas": "Всі області", "navigation": "Навігація", "openResult": "Відкрити результат", "showMore": "Показати більше", "placeholder": "Перейдіть або знайдіть що завгодно...", "noResultsTitle": "Результатів не знайдено", "noResultsMessage": "Ми не знайшли нічого, що відповідало б вашому запиту.", "emptySearchTitle": "Наберіть для пошуку", "emptySearchMessage": "Введіть ключове слово або фразу для пошуку.", "loadMore": "Заванта<PERSON>ити ще {{count}}", "groups": {"all": "Всі області", "customer": "Клієнти", "customerGroup": "Групи клієнтів", "product": "Продукти", "productVariant": "Варіанти продуктів", "inventory": "Інвентар", "reservation": "Резервац<PERSON><PERSON>", "category": "Категорії", "collection": "Колекції", "order": "Замовлення", "promotion": "Промоції", "campaign": "Кампанії", "priceList": "Цінові списки", "user": "Користувачі", "region": "Регіони", "taxRegion": "Податкові регіони", "returnReason": "Причини повернення", "salesChannel": "Канали продажу", "productType": "Типи продуктів", "productTag": "Теги продуктів", "location": "Локації", "shippingProfile": "Профілі доставки", "publishableApiKey": "Опубліковувані API ключі", "secretApiKey": "Таємні API ключі", "command": "Команди", "navigation": "Навігація"}}, "keyboardShortcuts": {"pageShortcut": "Перейти до", "settingShortcut": "Налаштування", "commandShortcut": "Команди", "then": "тоді", "navigation": {"goToOrders": "Замовлення", "goToProducts": "Продукти", "goToCollections": "Колекції", "goToCategories": "Категорії", "goToCustomers": "Клієнти", "goToCustomerGroups": "Групи клієнтів", "goToInventory": "Інвентар", "goToReservations": "Резервац<PERSON><PERSON>", "goToPriceLists": "Цінові списки", "goToPromotions": "Промоції", "goToCampaigns": "Кампанії"}, "settings": {"goToSettings": "Налаштування", "goToStore": "Мага<PERSON>ин", "goToUsers": "Користувачі", "goToRegions": "Регіони", "goToTaxRegions": "Податкові регіони", "goToSalesChannels": "Канали продажу", "goToProductTypes": "Типи продуктів", "goToLocations": "Локації", "goToPublishableApiKeys": "Опубліковувані API ключі", "goToSecretApiKeys": "Таємні API ключі", "goToWorkflows": "Робочі процеси", "goToProfile": "Профіль", "goToReturnReasons": "Причини повернення"}}, "menus": {"user": {"documentation": "Документація", "changelog": "Зм<PERSON><PERSON>и", "shortcuts": "Скорочення", "profileSettings": "Налаштування профілю", "theme": {"label": "Тема", "dark": "Темна", "light": "Світла", "system": "Система"}}, "store": {"label": "Мага<PERSON>ин", "storeSettings": "Налаштування магазину"}, "actions": {"logout": "Вийти"}}, "nav": {"accessibility": {"title": "Навігація", "description": "Меню навігації для панелі інструментів."}, "common": {"extensions": "Розширення"}, "main": {"store": "Мага<PERSON>ин", "storeSettings": "Налаштування магазину"}, "settings": {"header": "Налаштування", "general": "Загальні", "developer": "Розробник", "myAccount": "<PERSON><PERSON><PERSON> акаунт"}}}, "dataGrid": {"columns": {"view": "Перегляд", "resetToDefault": "Скинути до стандартних значень", "disabled": "Зміна видимих колонок вимкнена."}, "shortcuts": {"label": "Скорочення", "commands": {"undo": "Скасувати", "redo": "Повторити", "copy": "Копіювати", "paste": "Вставити", "edit": "Редагувати", "delete": "Видалити", "clear": "Очистити", "moveUp": "Перемістити вгору", "moveDown": "Перемістити вниз", "moveLeft": "Перемістити вліво", "moveRight": "Перемістити вправо", "moveTop": "Перемістити на верх", "moveBottom": "Перемістити на низ", "selectDown": "Вибрати вниз", "selectUp": "Вибрати вгору", "selectColumnDown": "Вибрати стовпчик вниз", "selectColumnUp": "Вибрати стовпчик вгору", "focusToolbar": "Перемістити фокус на панель інструментів", "focusCancel": "Перемістити фокус на скасування"}}, "errors": {"fixError": "Виправити помилку", "count_one": "{{count}} помилка", "count_other": "{{count}} помилок"}}, "filters": {"date": {"today": "Сьогодні", "lastSevenDays": "Останні 7 днів", "lastThirtyDays": "Останні 30 днів", "lastNinetyDays": "Останні 90 днів", "lastTwelveMonths": "Останні 12 місяців", "custom": "Індивідуально", "from": "З", "to": "До"}, "compare": {"lessThan": "Мен<PERSON>е ніж", "greaterThan": "Більше ніж", "exact": "Точно", "range": "Діапазон", "lessThanLabel": "менше ніж {{value}}", "greaterThanLabel": "більше ніж {{value}}", "andLabel": "і"}, "radio": {"yes": "Так", "no": "немає", "true": "правда", "false": "помилковий"}, "addFilter": "Додати фільтр"}, "errorBoundary": {"badRequestTitle": "400 - Не<PERSON><PERSON><PERSON><PERSON><PERSON> запит", "badRequestMessage": "Запит не може бути зрозумілим сервером через помилковий синтаксис.", "notFoundTitle": "404 - Сторінка не знайдена", "notFoundMessage": "Перевірте URL та спробуйте ще раз, або використайте пошуковий рядок для знаходження того, що ви шукаєте.", "internalServerErrorTitle": "500 - Внутрішня помилка сервера", "internalServerErrorMessage": "Сталася неочікувана помилка на сервері. Будь ласка, спробуйте ще раз пізніше.", "defaultTitle": "Сталася помилка", "defaultMessage": "Сталася неочікувана помилка під час рендерингу цієї сторінки.", "noMatchMessage": "Сторінка, яку ви шукаєте, не існує.", "backToDashboard": "Назад до панелі керування"}, "addresses": {"title": "Адреси", "shippingAddress": {"header": "Адреса доставки", "editHeader": "Редагувати адресу доставки", "editLabel": "Адреса доставки", "label": "Адреса доставки"}, "billingAddress": {"header": "Пла<PERSON><PERSON><PERSON>на адреса", "editHeader": "Редагувати платіжну адресу", "editLabel": "Пла<PERSON><PERSON><PERSON>на адреса", "label": "Пла<PERSON><PERSON><PERSON>на адреса", "sameAsShipping": "Така ж, як адреса доставки"}, "contactHeading": "Кон<PERSON><PERSON><PERSON>т", "locationHeading": "Місцезнаходження"}, "email": {"editHeader": "Редагувати електронну пошту", "editLabel": "Електронна пошта", "label": "Електронна пошта"}, "transferOwnership": {"header": "Перехід права власності", "label": "Перехід права власності", "details": {"order": "Деталі замовлення", "draft": "Деталі чернетки"}, "currentOwner": {"label": "Поточний власник", "hint": "Поточний власник замовлення."}, "newOwner": {"label": "Новий власник", "hint": "Новий власник, якому потрібно передати замовлення."}, "validation": {"mustBeDifferent": "Новий власник повинен відрізнятися від поточного власника.", "required": "Новий власник є обов'язковим."}}, "sales_channels": {"availableIn": "Доступно в <0>{{x}}</0> з <1>{{y}}</1> торгових каналів"}, "products": {"domain": "Продукти", "list": {"noRecordsMessage": "Створіть перший продукт, щоб почати продавати."}, "edit": {"header": "Редагувати продукт", "description": "Редагуйте деталі продукту.", "successToast": "Продукт {{title}} успішно оновлений."}, "create": {"title": "Створити продукт", "description": "Створіть новий продукт.", "header": "Основне", "tabs": {"details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "organize": "Організувати", "variants": "Варіанти", "inventory": "Комплекти на складі"}, "errors": {"variants": "Будь ласка, виберіть хоча б один варіант.", "options": "Будь ласка, створіть хоча б одну опцію.", "uniqueSku": "SKU має бути унікальним."}, "inventory": {"heading": "Комплекти на складі", "label": "Додайте елементи складу до комплекту інвентарю варіанту.", "itemPlaceholder": "Виберіть елемент складу", "quantityPlaceholder": "Скільки з цих елементів потрібно для комплекту?"}, "variants": {"header": "Варіанти", "subHeadingTitle": "Так, це продукт з варіантами", "subHeadingDescription": "Коли не обрано, ми створимо стандартний варіант для вас", "optionTitle": {"placeholder": "Розмір"}, "optionValues": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>, Середній, Великий"}, "productVariants": {"label": "Варіанти продукту", "hint": "Цей порядок впливатиме на порядок варіантів у вашому магазині.", "alert": "Додайте опції для створення варіантів.", "tip": "Необрані варіанти не будуть створені. Ви завжди можете створити та редагувати варіанти пізніше, але цей список підходить для варіацій ваших опцій продукту."}, "productOptions": {"label": "Опції продукту", "hint": "Визначте опції для продукту, наприклад, колір, розмір тощо."}}, "successToast": "Продукт {{title}} успішно створено."}, "export": {"header": "Експорт списку продуктів", "description": "Експортуйте список продуктів у CSV файл.", "success": {"title": "Ми обробляємо ваш експорт", "description": "Експортування даних може зайняти кілька хвилин. Ми повідомимо вас, коли завершимо."}, "filters": {"title": "Фільтри", "description": "Застосуйте фільтри у огляді таблиці, щоб налаштувати цей вигляд"}, "columns": {"title": "Колонки", "description": "Налаштуйте експортовані дані для задоволення специфічних потреб"}}, "import": {"header": "Імпорт списку продуктів", "uploadLabel": "Імпортувати продукти", "uploadHint": "Перетягніть CSV файл або натисніть, щоб завантажити", "description": "Імпортуйте продукти, надавши CSV файл у попередньо визначеному форматі", "template": {"title": "Не впевнені, як організувати ваш список?", "description": "Завантажте шаблон нижче, щоб переконатися, що ви дотримуєтесь правильного формату."}, "upload": {"title": "Завантажте CSV файл", "description": "За допомогою імпорту ви можете додавати або оновлювати продукти. Для оновлення існуючих продуктів потрібно використовувати існуючий хендл і ID, для оновлення існуючих варіантів потрібно використовувати існуючий ID. Вас попросять підтвердити перед імпортом продуктів.", "preprocessing": "Попередня обробка...", "productsToCreate": "Продукти будуть створені", "productsToUpdate": "Продукти будуть оновлені"}, "success": {"title": "Ми обробляємо ваш імпорт", "description": "Імпорт даних може зайняти деякий час. Ми повідомимо вас, коли завершимо."}}, "deleteWarning": "Ви збираєтеся видалити продукт {{title}}. Цю дію не можна скасувати.", "variants": {"header": "Варіанти", "empty": {"heading": "Немає варіантів", "description": "Немає варіантів для відображення."}, "filtered": {"heading": "Немає результатів", "description": "Немає варіа<PERSON><PERSON><PERSON><PERSON>, які відповідають поточному критерію фільтрації."}}, "attributes": "Атрибути", "editAttributes": "Редагувати атрибути", "editOptions": "Редагувати опції", "editPrices": "Редагувати ціни", "media": {"label": "Медіа", "editHint": "Додайте медіа до продукту, щоб продемонструвати його у вашому магазині.", "makeThumbnail": "Зробити мініатюру", "uploadImagesLabel": "Завантажити зображення", "uploadImagesHint": "Перетягніть зображення сюди або натисніть, щоб завантажити.", "invalidFileType": "'{{name}}' не підтримується. Підтримувані типи файлів: {{types}}.", "failedToUpload": "Не вдалося завантажити додане медіа. Спробуйте ще раз.", "deleteWarning_one": "Ви збираєтеся видалити {{count}} зображення. Цю дію не можна скасувати.", "deleteWarning_other": "Ви збираєтеся видалити {{count}} зображень. Цю дію не можна скасувати.", "deleteWarningWithThumbnail_one": "Ви збираєтеся видалити {{count}} зображення, включаючи мініатюру. Цю дію не можна скасувати.", "deleteWarningWithThumbnail_other": "Ви збираєтеся видалити {{count}} зображень, включаючи мініатюри. Цю дію не можна скасувати.", "thumbnailTooltip": "Мініатюра", "galleryLabel": "Галерея", "downloadImageLabel": "Завантажити поточне зображення", "deleteImageLabel": "Видалити поточне зображення", "emptyState": {"header": "Немає медіа", "description": "Додайте медіа до продукту, щоб продемонструвати його у вашому магазині.", "action": "Додати медіа"}, "successToast": "Медіа успішно оновлено."}, "discountableHint": "Коли не вибрано, знижки не будуть застосовуватися до цього продукту.", "noSalesChannels": "Не доступний у жодному каналі продажу", "variantCount_one": "{{count}} варіант", "variantCount_other": "{{count}} варіанти", "deleteVariantWarning": "Ви збираєтеся видалити варіант {{title}}. Цю дію не можна скасувати.", "productStatus": {"draft": "Чернетка", "published": "Опубліковано", "proposed": "Запропоновано", "rejected": "Від<PERSON>илено"}, "fields": {"title": {"label": "Назва", "hint": "Дайте своєму продукту коротку та чітку назву.<0/>Рекомендована довжина для пошукових систем: 50-60 символів.", "placeholder": "Зимова куртка"}, "subtitle": {"label": "Підзаголовок", "placeholder": "Тепло і затишно"}, "handle": {"label": "Ідентифікатор", "tooltip": "Ідентифікатор використовується для посилання на продукт у вашому магазині. Якщо не вказано, ідентифікатор буде згенерований із назви продукту.", "placeholder": "зимова-куртка"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Дайте своєму продукту короткий та чіткий опис.<0/>Рекомендована довжина для пошукових систем: 120-160 символів.", "placeholder": "Тепла і затишна куртка"}, "discountable": {"label": "Доступний для знижок", "hint": "Коли не вибрано, знижки не будуть застосовані до цього продукту"}, "type": {"label": "Тип"}, "collection": {"label": "Колекція"}, "categories": {"label": "Категорії"}, "tags": {"label": "Теги"}, "sales_channels": {"label": "Канали продажу", "hint": "Цей продукт буде доступний тільки в стандартному каналі продажу, якщо не буде змінено."}, "countryOrigin": {"label": "Країна походження"}, "material": {"label": "Ма<PERSON><PERSON><PERSON><PERSON><PERSON>л"}, "width": {"label": "Ши<PERSON><PERSON><PERSON>"}, "length": {"label": "Довжина"}, "height": {"label": "Висота"}, "weight": {"label": "Вага"}, "options": {"label": "Опції продукту", "hint": "Опції використовуються для визначення кольору, розміру тощо продукту", "add": "Додати опцію", "optionTitle": "Назва опції", "optionTitlePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variations": "Вар<PERSON><PERSON><PERSON><PERSON><PERSON> (через кому)", "variantionsPlaceholder": "Чер<PERSON><PERSON><PERSON><PERSON>, Синій, Зелений"}, "variants": {"label": "Варіанти продукту", "hint": "Вар<PERSON><PERSON><PERSON><PERSON><PERSON>, для яких не обрано, не будуть створені. Це вплине на порядок відображення варіантів на вашому сайті."}, "mid_code": {"label": "Mid код"}, "hs_code": {"label": "HS код"}}, "variant": {"edit": {"header": "Редагувати варіант", "success": "Варіант продукту успішно відредаговано"}, "create": {"header": "Дета<PERSON><PERSON> варіанту"}, "deleteWarning": "Ви впевнені, що хочете видалити цей варіант?", "pricesPagination": "1 - {{current}} із {{total}} цін", "tableItemAvailable": "{{availableCount}} доступно", "tableItem_one": "{{availableCount}} доступно на {{locationCount}} локації", "tableItem_other": "{{availableCount}} доступно на {{locationCount}} локаціях", "inventory": {"notManaged": "Не керується", "manageItems": "Керувати товарами на складі", "notManagedDesc": "Запаси для цього варіанту не керуються. Увімкніть 'Керування запасами', щоб відслідковувати запаси варіанту.", "manageKit": "Керувати комплектом запасів", "navigateToItem": "Перейти до товару на складі", "actions": {"inventoryItems": "Перейти до товару на складі", "inventoryKit": "Показати товари на складі"}, "inventoryKit": "Комплект запасів", "inventoryKitHint": "Цей варіант складається з кількох товарів на складі?", "validation": {"itemId": "Будь ласка, виберіть товар на складі.", "quantity": "Кількість обов'язкова. Введіть позитивне число."}, "header": "Запаси та інвентар", "editItemDetails": "Редагувати деталі товару", "manageInventoryLabel": "Керувати запасами", "manageInventoryHint": "Коли увімкнено, ми будемо змінювати кількість товару при створенні замовлень та повернень.", "allowBackordersLabel": "Дозволити замовлення на складі", "allowBackordersHint": "Коли увімкнено, клієнти можуть замовити варіант, навіть якщо кількість недоступна.", "toast": {"levelsBatch": "Рівні запасів оновлено.", "update": "Товар на складі успішно оновлено.", "updateLevel": "Рівень запасів успішно оновлено.", "itemsManageSuccess": "Товари на складі успішно оновлено."}}}, "options": {"header": "Опції", "edit": {"header": "Редагувати опцію", "successToast": "Опція {{title}} успішно оновлена."}, "create": {"header": "Створити опцію", "successToast": "Опція {{title}} успішно створена."}, "deleteWarning": "Ви збираєтеся видалити опцію продукту: {{title}}. Цю дію неможливо скасувати."}, "organization": {"header": "Організація", "edit": {"header": "Редагувати організацію", "toasts": {"success": "Організацію {{title}} успішно оновлено."}}}, "toasts": {"delete": {"success": {"header": "Продукт видалено", "description": "{{title}} успішно видалено."}, "error": {"header": "Не вдалося видалити продукт"}}}}, "collections": {"domain": "Колекції", "subtitle": "Організуйте продукти у колекції.", "createCollection": "Створити колекцію", "createCollectionHint": "Створіть нову колекцію для організації ваших продуктів.", "createSuccess": "Колекцію створено успішно.", "editCollection": "Редагувати колекцію", "handleTooltip": "Обробник використовується для посилання на колекцію у вашому магазині. Якщо не вказано, обробник буде згенеровано з назви колекції.", "deleteWarning": "Ви збираєтесь видалити колекцію {{title}}. Цю дію не можна скасувати.", "removeSingleProductWarning": "Ви збираєтесь видалити продукт {{title}} з колекції. Цю дію не можна скасувати.", "removeProductsWarning_one": "Ви збираєтесь видалити {{count}} продукт з колекції. Цю дію не можна скасувати.", "removeProductsWarning_other": "Ви збираєтесь видалити {{count}} продукти з колекції. Цю дію не можна скасувати.", "products": {"list": {"noRecordsMessage": "В колекції немає продуктів."}, "add": {"successToast_one": "Продукт успішно додано до колекції.", "successToast_other": "Продукти успішно додано до колекції."}, "remove": {"successToast_one": "Продукт успішно видалено з колекції.", "successToast_other": "Продукти успішно видалено з колекції."}}}, "categories": {"domain": "Категорії", "subtitle": "Організуйте продукти в категорії та керуйте їх рейтингом і ієрархією.", "create": {"header": "Створити категорію", "hint": "Створіть нову категорію для організації ваших продуктів.", "tabs": {"details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "organize": "Організувати рейтинг"}, "successToast": "Категор<PERSON>я {{name}} успішно створена."}, "edit": {"header": "Редагувати категорію", "description": "Редагуйте категорію, щоб оновити її деталі.", "successToast": "Категорія успішно оновлена."}, "delete": {"confirmation": "Ви збираєтесь видалити категорію {{name}}. Цю дію не можна скасувати.", "successToast": "Категор<PERSON>я {{name}} успішно видалена."}, "products": {"add": {"disabledTooltip": "Продукт вже є в цій категорії.", "successToast_one": "Додано {{count}} продукт до категорії.", "successToast_other": "Додано {{count}} продукти до категорії."}, "remove": {"confirmation_one": "Ви збираєтесь видалити {{count}} продукт з категорії. Цю дію не можна скасувати.", "confirmation_other": "Ви збираєтесь видалити {{count}} продукти з категорії. Цю дію не можна скасувати.", "successToast_one": "Видалено {{count}} продукт з категорії.", "successToast_other": "Видалено {{count}} продукти з категорії."}, "list": {"noRecordsMessage": "В категорії немає продуктів."}}, "organize": {"header": "Організувати", "action": "Редагувати рейтинг"}, "fields": {"visibility": {"label": "Видимість", "internal": "Внутр<PERSON><PERSON><PERSON><PERSON>й", "public": "Публічний"}, "status": {"label": "Статус", "active": "Активний", "inactive": "Неактивний"}, "path": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Показати повний шлях категорії."}, "children": {"label": "Діти"}, "new": {"label": "Нова"}}}, "inventory": {"domain": "Інвентаризація", "subtitle": "Керуйте вашими товарними запасами", "reserved": "Зарезервовано", "available": "Доступно", "locationLevels": "Локації", "associatedVariants": "Пов'язані варіанти", "manageLocations": "Керувати локаціями", "deleteWarning": "Ви збираєтесь видалити товарний елемент. Цю дію не можна скасувати.", "editItemDetails": "Редагувати деталі товару", "create": {"title": "Створити товарний елемент", "details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "availability": "Наявність", "locations": "Локації", "attributes": "Атрибути", "requiresShipping": "Потрібна доставка", "requiresShippingHint": "Чи потребує товарна одиниця доставки?", "successToast": "Товарний елемент успішно створений."}, "reservation": {"header": "Резервування {{itemName}}", "editItemDetails": "Редагувати резервування", "lineItemId": "ID товарної одиниці", "orderID": "ID замовлення", "description": "<PERSON><PERSON><PERSON><PERSON>", "location": "Локація", "inStockAtLocation": "В наявності на цій локації", "availableAtLocation": "Доступно на цій локації", "reservedAtLocation": "Зарезервовано на цій локації", "reservedAmount": "Кількість для резервування", "create": "Створити резервування", "itemToReserve": "Товар для резервування", "quantityPlaceholder": "Скільки ви хочете зарезервувати?", "descriptionPlaceholder": "Який тип резервування?", "successToast": "Резервування успішно створено.", "updateSuccessToast": "Резервування успішно оновлено.", "deleteSuccessToast": "Резервування успішно видалено.", "errors": {"noAvaliableQuantity": "Локація не має доступної кількості.", "quantityOutOfRange": "Мінімальна кількість — 1, максимальна кількість — {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Кількість на складі не може бути оновлена до менше ніж зарезервована кількість {{quantity}}."}}, "toast": {"updateLocations": "Локації успішно оновлені.", "updateLevel": "Рівень інвентарю успішно оновлено.", "updateItem": "Товарний елемент успішно оновлено."}}, "giftCards": {"domain": "Подарункові картки", "editGiftCard": "Редагувати подарункову картку", "createGiftCard": "Створити подарункову картку", "createGiftCardHint": "Створіть подарункову картку вручну, яку можна використовувати як метод оплати у вашому магазині.", "selectRegionFirst": "Спочатку виберіть регіон", "deleteGiftCardWarning": "Ви збираєтеся видалити подарункову картку {{code}}. Цю дію не можна скасувати.", "balanceHigherThanValue": "Баланс не може бути більшим за початкову суму.", "balanceLowerThanZero": "Баланс не може бути від'ємним.", "expiryDateHint": "У різних країнах існують різні закони щодо термінів дії подарункових карток. Переконайтесь, що перевірили місцеві регуляції перед встановленням терміну дії.", "regionHint": "Зміна регіону подарункової картки також змінить її валюту, що може вплинути на її грошову вартість.", "enabledHint": "Вка<PERSON><PERSON>ть, чи активована подарункова картка.", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentBalance": "Поточний баланс", "initialBalance": "Початковий баланс", "personalMessage": "Особисте повідомлення", "recipient": "Од<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customers": {"domain": "Клієнти", "list": {"noRecordsMessage": "Ваші клієнти з'являться тут."}, "create": {"header": "Створити клієнта", "hint": "Створіть нового клієнта та керуйте його даними.", "successToast": "Клієнта {{email}} було успішно створено."}, "groups": {"label": "Групи клієнтів", "remove": "Ви впевнені, що хочете видалити клієнта з групи \"{{name}}\"?", "removeMany": "Ви впевнені, що хочете видалити клієнта з наступних груп: {{groups}}?", "alreadyAddedTooltip": "Клієнт вже є в цій групі.", "list": {"noRecordsMessage": "Цей клієнт не належить до жодної групи."}, "add": {"success": "Клієнт доданий до: {{groups}}.", "list": {"noRecordsMessage": "Будь ласка, створіть групу клієнтів спочатку."}}, "removed": {"success": "Клієнт видалений з: {{groups}}.", "list": {"noRecordsMessage": "Будь ласка, створіть групу клієнтів спочатку."}}}, "edit": {"header": "Редагувати клієнта", "emailDisabledTooltip": "Адресу електронної пошти не можна змінити для зареєстрованих клієнтів.", "successToast": "Клієнта {{email}} було успішно оновлено."}, "delete": {"title": "Видалити клієнта", "description": "Ви збираєтеся видалити клієнта {{email}}. Цю дію не можна скасувати.", "successToast": "Клієнта {{email}} було успішно видалено."}, "fields": {"guest": "Гість", "registered": "Зареєстрований", "groups": "Гру<PERSON>и"}, "registered": "Зареєстрований", "guest": "Гість", "hasAccount": "Має акаунт"}, "customerGroups": {"domain": "Групи клієнтів", "subtitle": "Організуйте клієнтів у групи. Кожна група може мати різні акції та ціни.", "create": {"header": "Створити групу клієнтів", "hint": "Створіть нову групу клієнтів для сегментації вашої аудиторії.", "successToast": "Групу клієнтів {{name}} було успішно створено."}, "edit": {"header": "Редагувати групу клієнтів", "successToast": "Групу клієнтів {{name}} було успішно оновлено."}, "delete": {"title": "Видалити групу клієнтів", "description": "Ви збираєтеся видалити групу клієнтів {{name}}. Цю дію не можна скасувати.", "successToast": "Групу клієнтів {{name}} було успішно видалено."}, "customers": {"alreadyAddedTooltip": "Клієнт вже доданий до групи.", "add": {"successToast_one": "Клієнта успішно додано до групи.", "successToast_other": "Клієнтів успішно додано до групи.", "list": {"noRecordsMessage": "Спочатку створіть групу клієнтів."}}, "remove": {"title_one": "Видалити клієнта", "title_other": "Видалити клієнтів", "description_one": "Ви збираєтеся видалити {{count}} клієнта з групи. Цю дію не можна скасувати.", "description_other": "Ви збираєтеся видалити {{count}} клієнтів з групи. Цю дію не можна скасувати."}, "list": {"noRecordsMessage": "У цій групі немає клієнтів."}}}, "orders": {"domain": "Замовлення", "claim": "Претензія", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "Повернення", "cancelWarning": "Ви збираєтеся скасувати замовлення {{id}}. Цю дію неможливо скасувати.", "onDateFromSalesChannel": "{{date}} з {{salesChannel}}", "list": {"noRecordsMessage": "Ваші замовлення з'являться тут."}, "summary": {"requestReturn": "Запросити повернення", "allocateItems": "Розподілити товари", "editOrder": "Редагувати замовлення", "editOrderContinue": "Продовжити редагування замовлення", "inventoryKit": "Складається з {{count}}x товарів на складі", "itemTotal": "Загальна сума товарів", "shippingTotal": "Загальна сума доставки", "discountTotal": "Загальна сума знижки", "taxTotalIncl": "Загальна сума податків (включно)", "itemSubtotal": "Проміжна сума товарів", "shippingSubtotal": "Проміжна сума доставки", "discountSubtotal": "Проміжна сума знижки", "taxTotal": "Загальна сума податків"}, "transfer": {"title": "Передати право власності", "requestSuccess": "Запит на передачу замовлення надіслано на: {{email}}.", "currentOwner": "Поточний власник", "newOwner": "Новий власник", "currentOwnerDescription": "Клієнт, який наразі пов'язаний з цим замовленням.", "newOwnerDescription": "Клієнт, якому буде передано це замовлення."}, "payment": {"title": "<PERSON>ла<PERSON><PERSON><PERSON><PERSON>", "isReadyToBeCaptured": "Пл<PERSON><PERSON><PERSON><PERSON> <0/> готовий до підтвердження.", "totalPaidByCustomer": "Загальна сума, сплачена клієнтом", "capture": "Підтвердити платіж", "capture_short": "Підтвердити", "refund": "Відшкодувати", "markAsPaid": "Позначити як оплачено", "statusLabel": "Статус платежу", "statusTitle": "Статус платежу", "status": {"notPaid": "Не оплачено", "authorized": "Авторизовано", "partiallyAuthorized": "Частково авторизовано", "awaiting": "Очікує", "captured": "Підтверджено", "partiallyRefunded": "Частково відшкодовано", "partiallyCaptured": "Частково підтверджено", "refunded": "Відшкодовано", "canceled": "Скасовано", "requiresAction": "Потрібна дія"}, "capturePayment": "Платіж на суму {{amount}} буде підтверджено.", "capturePaymentSuccess": "Платіж на суму {{amount}} успішно підтверджено", "markAsPaidPayment": "Платіж на суму {{amount}} буде позначено як оплачено.", "markAsPaidPaymentSuccess": "Платіж на суму {{amount}} успішно позначено як оплачено", "createRefund": "Створити відшкодування", "refundPaymentSuccess": "Відшкодування суми {{amount}} успішне", "createRefundWrongQuantity": "Кількість повинна бути числом від 1 до {{number}}", "refundAmount": "Відшкодування {{ amount }}", "paymentLink": "Скопіювати посилання для платежу на {{ amount }}", "selectPaymentToRefund": "Виберіть платіж для відшкодування"}, "edits": {"title": "Редагувати замовлення", "confirm": "Підтвердити редагування", "confirmText": "Ви збираєтеся підтвердити редагування замовлення. Цю дію неможливо скасувати.", "cancel": "Скасувати редагування", "currentItems": "Поточні товари", "currentItemsDescription": "Відкоригуйте кількість товарів або видаліть.", "addItemsDescription": "Ви можете додати нові товари до замовлення.", "addItems": "Додати товари", "amountPaid": "Сплачена сума", "newTotal": "Нова сума", "differenceDue": "Різниця до сплати", "create": "Редагувати замовлення", "currentTotal": "Поточна сума", "noteHint": "Додайте внутрішню примітку до редагування", "cancelSuccessToast": "Редагування замовлення скасовано", "createSuccessToast": "Запит на редагування замовлення створено", "activeChangeError": "На замовленні вже є активні зміни (повернення, претензія, обмін тощо). Завершіть або скасуйте зміни перед редагуванням замовлення.", "panel": {"title": "Запит на редагування замовлення", "titlePending": "Редагування замовлення в очікуванні"}, "toast": {"canceledSuccessfully": "Редагування замовлення скасовано", "confirmedSuccessfully": "Редагування замовлення підтверджено"}, "validation": {"quantityLowerThanFulfillment": "Неможливо встановити кількість, яка менша або дорівнює виконаній кількості"}}, "edit": {"email": {"title": "Редагувати email", "requestSuccess": "Email замовлення оновлено на {{email}}."}, "shippingAddress": {"title": "Редагувати адресу доставки", "requestSuccess": "Адреса доставки замовлення оновлена."}, "billingAddress": {"title": "Редагувати адресу для рахунку", "requestSuccess": "Адреса для рахунку замовлення оновлена."}}, "returns": {"create": "Створити повернення", "confirm": "Підтвердити повернення", "confirmText": "Ви збираєтеся підтвердити повернення. Цю дію неможливо скасувати.", "inbound": "Вхідний", "outbound": "Вихідний", "sendNotification": "Відправити сповіщення", "sendNotificationHint": "Сповістіть клієнта про повернення.", "returnTotal": "Загальна сума повернення", "inboundTotal": "Загальна сума вхідних товарів", "refundAmount": "Сума відшкодування", "outstandingAmount": "Невідшкодована сума", "reason": "Причина", "reasonHint": "Виберіть причину, чому клієнт хоче повернути товар.", "note": "Примітка", "noInventoryLevel": "Немає рівня запасів", "noInventoryLevelDesc": "Вибране місце не має рівня запасів для вибраних товарів. Повернення може бути запитано, але не може бути прийняте, поки не створений рівень запасів для цього місця.", "noteHint": "Ви можете вільно вказати щось, якщо хочете уточнити.", "location": "Місце", "locationHint": "Виберіть місце, до якого хочете повернути товари.", "inboundShipping": "Доставка повернення", "inboundShippingHint": "Виберіть метод доставки для повернення.", "returnableQuantityLabel": "Кількість товарів для повернення", "refundableAmountLabel": "Сума для відшкодування", "returnRequestedInfo": "Запитано повернення {{requestedItemsCount}}x товарів", "returnReceivedInfo": "Прийнято повернення {{requestedItemsCount}}x товарів", "itemReceived": "Товари прийняті", "returnRequested": "Повернення запитано", "damagedItemReceived": "Прийнято пошкоджені товари", "damagedItemsReturned": "{{quantity}}x пошкоджених товарів повернуто", "activeChangeError": "На цьому замовленні є активні зміни. Завершіть або відхиліть зміни перед прийняттям повернення.", "cancel": {"title": "Скасувати повернення", "description": "Ви впевнені, що хочете скасувати запит на повернення?"}, "placeholders": {"noReturnShippingOptions": {"title": "Не знайдено варіантів доставки для повернення", "hint": "Для цього місця не створено варіантів доставки для повернення. Ви можете створити їх у <LinkComponent>Місце та доставка</LinkComponent>."}, "outboundShippingOptions": {"title": "Не знайдено варіантів доставки для відправлення", "hint": "Для цього місця не створено варіантів доставки для відправлення. Ви можете створити їх у <LinkComponent>Місце та доставка</LinkComponent>."}}, "receive": {"action": "Прийняти товари", "receiveItems": "{{returnType}} {{id}}", "restockAll": "Поповнити всі товари", "itemsLabel": "Прийняті товари", "title": "Прийняти товари для #{{returnId}}", "sendNotificationHint": "Сповістіть клієнта про прийняті товари.", "inventoryWarning": "Зверніть увагу, що ми автоматично скоригуємо рівні запасів на основі ваших даних.", "writeOffInputLabel": "Скільки товарів пошкоджено?", "toast": {"success": "Повернення прийнято успішно.", "errorLargeValue": "Кількість більша за запитану кількість товарів.", "errorNegativeValue": "Кількість не може бути від'ємною.", "errorLargeDamagedValue": "Кількість пошкоджених товарів + непошкоджених прийнятих товарів перевищує загальну кількість товарів у поверненні. Зменшіть кількість непошкоджених товарів."}}, "toast": {"canceledSuccessfully": "Повернення успішно скасовано", "confirmedSuccessfully": "Повернення успішно підтверджено"}, "panel": {"title": "Повернення ініційоване", "description": "Є відкритий запит на повернення для цього товару."}}, "claims": {"create": "Створити заявку", "confirm": "Підтвердити заявку", "confirmText": "Ви збираєтеся підтвердити заявку. Цю дію неможливо скасувати.", "manage": "Керувати заявкою", "outbound": "Відправка", "outboundItemAdded": "{{itemsCount}}x додано через заявку", "outboundTotal": "Загальна відправка", "outboundShipping": "Вартість відправки", "outboundShippingHint": "Оберіть метод відправки, який ви хочете використати.", "refundAmount": "Оціночна різниця", "activeChangeError": "Є активна зміна замовлення. Завершіть або скиньте попередню зміну.", "actions": {"cancelClaim": {"successToast": "Заявка була успішно скасована."}}, "cancel": {"title": "Скасувати заявку", "description": "Ви впевнені, що хочете скасувати заявку?"}, "tooltips": {"onlyReturnShippingOptions": "Цей список міститиме лише варіанти повернення товарів."}, "toast": {"canceledSuccessfully": "Заявка успішно скасована", "confirmedSuccessfully": "Заявка успішно підтверджена"}, "panel": {"title": "Заявка ініційована", "description": "Є відкрита заявка, яку потрібно завершити"}}, "exchanges": {"create": "Створити обмін", "manage": "Керувати обміном", "confirm": "Підтвердити обмін", "confirmText": "Ви збираєтеся підтвердити обмін. Цю дію неможливо скасувати.", "outbound": "Відправка", "outboundItemAdded": "{{itemsCount}}x додано через обмін", "outboundTotal": "Загальна відправка", "outboundShipping": "Вартість відправки", "outboundShippingHint": "Оберіть метод відправки, який ви хочете використати.", "refundAmount": "Оціночна різниця", "activeChangeError": "Є активна зміна замовлення. Завершіть або скиньте попередню зміну.", "actions": {"cancelExchange": {"successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> був успішно скасований."}}, "cancel": {"title": "Скасувати обмін", "description": "Ви впевнені, що хочете скасувати обмін?"}, "tooltips": {"onlyReturnShippingOptions": "Цей список міститиме лише варіанти повернення товарів."}, "toast": {"canceledSuccessfully": "Об<PERSON>ін успішно скасовано", "confirmedSuccessfully": "Об<PERSON><PERSON>н успішно підтверджено"}, "panel": {"title": "Обмін ініційований", "description": "Є відкритий запит на обмін, який потрібно завершити"}}, "reservations": {"allocatedLabel": "Виділено", "notAllocatedLabel": "Не виділено"}, "allocateItems": {"action": "Виділити товари", "title": "Виділити товари замовлення", "locationDescription": "Оберіть місце, з якого ви хочете виділити товари.", "itemsToAllocate": "Товари для виділення", "itemsToAllocateDesc": "Оберіть кількість товарів, яку ви хочете виділити", "search": "Шукати товари", "consistsOf": "Містить {{num}}x товарів на складі", "requires": "Потр<PERSON>бно {{num}} на кожен варіант", "toast": {"created": "Товари успішно виділені"}, "error": {"quantityNotAllocated": "Є невиділені товари."}}, "shipment": {"title": "Позначити відправлення як відправлене", "trackingNumber": "Номер відстеження", "addTracking": "Додати номер відстеження", "sendNotification": "Надіслати сповіщення", "sendNotificationHint": "Сповістіть клієнта про це відправлення.", "toastCreated": "Відправлення успішно створено."}, "fulfillment": {"cancelWarning": "Ви збираєтеся скасувати виконання замовлення. Цю дію неможливо скасувати.", "markAsDeliveredWarning": "Ви збираєтеся позначити виконання замовлення як доставлене. Цю дію неможливо скасувати.", "unfulfilledItems": "Невиконані товари", "statusLabel": "Статус виконання", "statusTitle": "Статус виконання", "fulfillItems": "Виконати товари", "awaitingFulfillmentBadge": "Очікує виконання", "requiresShipping": "Потр<PERSON>бна відправка", "number": "Виконання #{{number}}", "itemsToFulfill": "Товари для виконання", "create": "Створити виконання", "available": "Доступно", "inStock": "В наявності", "markAsShipped": "Позначити як відправлене", "markAsDelivered": "Позначити як доставлене", "itemsToFulfillDesc": "Оберіть товари та кількості для виконання", "locationDescription": "Оберіть місце, з якого ви хочете виконати товари.", "sendNotificationHint": "Сповістіть клієнтів про створене виконання.", "methodDescription": "Оберіть інший метод відправки, ніж той, який обрав клієнт", "error": {"wrongQuantity": "Доступний тільки один товар для виконання", "wrongQuantity_other": "Кількість повинна бути числом між 1 і {{number}}", "noItems": "Немає товарів для виконання."}, "status": {"notFulfilled": "Не виконано", "partiallyFulfilled": "Частково виконано", "fulfilled": "Виконано", "partiallyShipped": "Частково відправлено", "shipped": "Відправлено", "delivered": "Доставлено", "partiallyDelivered": "Частково доставлено", "partiallyReturned": "Частково повернуто", "returned": "Повернуто", "canceled": "Скасовано", "requiresAction": "Потрібна дія"}, "toast": {"created": "Виконання успішно створено", "canceled": "Виконання успішно скасовано", "fulfillmentShipped": "Не можна скасувати виконання, яке вже було відправлене", "fulfillmentDelivered": "Виконання позначено як доставлене успішно"}, "trackingLabel": "Відстеження", "shippingFromLabel": "Відправка з", "itemsLabel": "Товари"}, "refund": {"title": "Створити повернення коштів", "sendNotificationHint": "Сповістіть клієнтів про створене повернення коштів.", "systemPayment": "Системний платіж", "systemPaymentDesc": "Один або кілька ваших платежів є системним платежем. Будь ласка, зверніть увагу, що системи Medusa не обробляють повернення коштів для таких платежів.", "error": {"amountToLarge": "Неможливо повернути більше, ніж оригінальна сума замовлення.", "amountNegative": "Сума повернення повинна бути позитивною.", "reasonRequired": "Будь ласка, виберіть причину повернення коштів."}}, "customer": {"contactLabel": "Кон<PERSON><PERSON><PERSON>т", "editEmail": "Редагувати електронну пошту", "transferOwnership": "Перенести власність", "editBillingAddress": "Редагувати платіжну адресу", "editShippingAddress": "Редагувати адресу доставки"}, "activity": {"header": "Активність", "showMoreActivities_one": "Показати ще {{count}} активність", "showMoreActivities_other": "Показати ще {{count}} активностей", "comment": {"label": "Коментар", "placeholder": "Залиште коментар", "addButtonText": "Додати коментар", "deleteButtonText": "Видалити коментар"}, "from": "<PERSON>ід", "to": "До", "events": {"common": {"toReturn": "Для повернення", "toSend": "Для відправлення"}, "placed": {"title": "Замовлення розміщено", "fromSalesChannel": "з {{salesChannel}}"}, "canceled": {"title": "Замовлення скасовано"}, "payment": {"awaiting": "Очікує на оплату", "captured": "Оплата отримана", "canceled": "Оплата скасована", "refunded": "Оплата повернута"}, "fulfillment": {"created": "Товари виконано", "canceled": "Виконання скасовано", "shipped": "Товари відправлено", "delivered": "Товари доставлено", "items_one": "{{count}} товар", "items_other": "{{count}} товари"}, "return": {"created": "Повернення #{{returnId}} запитано", "canceled": "Повернення #{{returnId}} скасовано", "received": "Повернення #{{returnId}} отримано", "items_one": "{{count}} товар повернуто", "items_other": "{{count}} товари повернуто"}, "note": {"comment": "Коментар", "byLine": "від {{author}}"}, "claim": {"created": "Претензія #{{claimId}} запитана", "canceled": "Претензія #{{claimId}} скасована", "itemsInbound": "{{count}} товар для повернення", "itemsOutbound": "{{count}} товар для відправлення"}, "exchange": {"created": "Обмін #{{exchangeId}} запитано", "canceled": "Обмін #{{exchangeId}} скасовано", "itemsInbound": "{{count}} товар для повернення", "itemsOutbound": "{{count}} товар для відправлення"}, "edit": {"requested": "Редагування замовлення #{{editId}} запитано", "confirmed": "Редагування замовлення #{{editId}} підтверджено"}, "transfer": {"requested": "Передача замовлення #{{transferId}} запитана", "confirmed": "Передача замовлення #{{transferId}} підтверджена", "declined": "Передача замовлення #{{transferId}} відхилена"}, "update_order": {"shipping_address": "Адресу доставки оновлено", "billing_address": "Платіжну адресу оновлено", "email": "Електронну пошту оновлено"}}}, "fields": {"displayId": "ID відображення", "refundableAmount": "Сума для повернення", "returnableQuantity": "Кількість для повернення"}}, "draftOrders": {"domain": "Чернетки замовлень", "deleteWarning": "Ви збираєтеся видалити чернетку замовлення {{id}}. Цю дію не можна скасувати.", "paymentLinkLabel": "Посилання на оплату", "cartIdLabel": "ID кошика", "markAsPaid": {"label": "Позначити як оплачено", "warningTitle": "Позначити як оплачено", "warningDescription": "Ви збираєтеся позначити чернетку замовлення як оплачено. Цю дію не можна скасувати, і подальше збирання платежу буде неможливим."}, "status": {"open": "Відкрите", "completed": "Завершене"}, "create": {"createDraftOrder": "Створити чернетку замовлення", "createDraftOrderHint": "Створіть нову чернетку замовлення для управління деталями замовлення до його оформлення.", "chooseRegionHint": "Виберіть регіон", "existingItemsLabel": "Існуючі товари", "existingItemsHint": "Додайте існуючі продукти до чернетки замовлення.", "customItemsLabel": "Індивідуальні товари", "customItemsHint": "Додайте індивідуальні товари до чернетки замовлення.", "addExistingItemsAction": "Додати існуючі товари", "addCustomItemAction": "Додати індивідуальний товар", "noCustomItemsAddedLabel": "Ще не додано індивідуальних товарів", "noExistingItemsAddedLabel": "Ще не додано існуючих товарів", "chooseRegionTooltip": "Спочатку виберіть регіон", "useExistingCustomerLabel": "Використовувати існуючого клієнта", "addShippingMethodsAction": "Додати методи доставки", "unitPriceOverrideLabel": "Перевизначити ціну за одиницю", "shippingOptionLabel": "Опція доставки", "shippingOptionHint": "Виберіть опцію доставки для чернетки замовлення.", "shippingPriceOverrideLabel": "Перевизначити ціну доставки", "shippingPriceOverrideHint": "Перевизначте ціну доставки для чернетки замовлення.", "sendNotificationLabel": "Відправити повідомлення", "sendNotificationHint": "Надіслати повідомлення клієнту, коли чернетка замовлення буде створена."}, "validation": {"requiredEmailOrCustomer": "Потрібно вказати електронну пошту або клієнта.", "requiredItems": "Необхідно додати хоча б один товар.", "invalidEmail": "Електронна пошта повинна бути дійсною адресою."}}, "stockLocations": {"domain": "Локації та доставка", "list": {"description": "Керуйте локаціями складу та варіантами доставки вашого магазину."}, "create": {"header": "Створити локацію складу", "hint": "Локація складу — це фізичне місце, де зберігаються продукти та з якого вони відправляються.", "successToast": "Локація {{name}} була успішно створена."}, "edit": {"header": "Редагувати локацію складу", "viewInventory": "Переглянути інвентар", "successToast": "Локація {{name}} була успішно оновлена."}, "delete": {"confirmation": "Ви збираєтеся видалити локацію складу {{name}}. Цю дію не можна скасувати."}, "fulfillmentProviders": {"header": "Постачальники виконання замовлень", "shippingOptionsTooltip": "Цей випадаючий список містить лише постачальників, активованих для цієї локації. Додайте їх до локації, якщо список вимкнений.", "label": "Підключені постачальники виконання замовлень", "connectedTo": "Підключено до {{count}} з {{total}} постачальників виконання замовлень", "noProviders": "Ця локація складу не підключена до постачальників виконання замовлень.", "action": "Підключити постачальників", "successToast": "Постачальники виконання замовлень для локації складу були успішно оновлені."}, "fulfillmentSets": {"pickup": {"header": "Самовивіз"}, "shipping": {"header": "Доставка"}, "disable": {"confirmation": "Ви впевнені, що хочете вимкнути {{name}}? Це призведе до видалення всіх пов'язаних зон обслуговування та варіантів доставки, і цю дію не можна скасувати.", "pickup": "Самовивіз був успішно вимкнений.", "shipping": "Доставка була успішно вимкнена."}, "enable": {"pickup": "Самовивіз був успішно увімкнений.", "shipping": "Доставка була успішно увімкнена."}}, "sidebar": {"header": "Налаштування доставки", "shippingProfiles": {"label": "Профілі доставки", "description": "Групуйте продукти за вимогами до доставки"}}, "salesChannels": {"header": "Канали продажу", "label": "Підключені канали продажу", "connectedTo": "Підключено до {{count}} з {{total}} каналів продажу", "noChannels": "Локація не підключена до жодного каналу продажу.", "action": "Підключити канали продажу", "successToast": "Канали продажу були успішно оновлені."}, "shippingOptions": {"create": {"shipping": {"header": "Створити варіант доставки для {{zone}}", "hint": "Створіть новий варіант доставки, щоб визначити, як продукти будуть доставлятися з цієї локації.", "label": "Варіанти доставки", "successToast": "Варіант доставки {{name}} був успішно створений."}, "returns": {"header": "Створити варіант повернення для {{zone}}", "hint": "Створіть новий варіант повернення, щоб визначити, як продукти будуть повертатися на цю локацію.", "label": "Варіанти повернення", "successToast": "Варіант повернення {{name}} був успішно створений."}, "tabs": {"details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "prices": "Ціни"}, "action": "Створити варіант"}, "delete": {"confirmation": "Ви збираєтеся видалити варіант доставки {{name}}. Цю дію не можна скасувати.", "successToast": "Варіант доставки {{name}} був успішно видалений."}, "edit": {"header": "Редагувати варіант доставки", "action": "Редагувати варіант", "successToast": "Варіант доставки {{name}} був успішно оновлений."}, "pricing": {"action": "Редагувати ціни"}, "conditionalPrices": {"header": "Умовні ціни для {{name}}", "description": "Керуйте умовними цінами для цього варіанту доставки на основі загальної вартості товарів у кошику.", "attributes": {"cartItemTotal": "Загальна сума товарів у кошику"}, "summaries": {"range": "Я<PERSON><PERSON>о <0>{{attribute}}</0> між <1>{{gte}}</1> та <2>{{lte}}</2>", "greaterThan": "Якщо <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Якщо <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Додати ціну", "manageConditionalPrices": "Керувати умовними цінами"}, "rules": {"amount": "Ціна варіанту доставки", "gte": "Мінімальна загальна сума товарів у кошику", "lte": "Максимальна загальна сума товарів у кошику"}, "customRules": {"label": "Користувацькі правила", "tooltip": "Ці умовні ціни мають правила, які не можна управляти на панелі керування.", "eq": "Загальна сума товарів у кошику повинна бути рівною", "gt": "Загальна сума товарів у кошику повинна бути більшою за", "lt": "Загальна сума товарів у кошику повинна бути меншою за"}, "errors": {"amountRequired": "Ціна варіанту доставки є обов'язковою", "minOrMaxRequired": "Потрібно вказати хоча б мінімальну або максимальну загальну суму товарів у кошику", "minGreaterThanMax": "Мінімальна сума товарів у кошику повинна бути меншою або рівною максимальній сумі товарів у кошику", "duplicateAmount": "Ціна варіанту доставки повинна бути унікальною для кожної умови", "overlappingConditions": "Умови повинні бути унікальними для всіх правил цін"}}, "fields": {"count": {"shipping_one": "{{count}} варіант доставки", "shipping_other": "{{count}} варіанти доставки", "returns_one": "{{count}} варіант повернення", "returns_other": "{{count}} варіанти повернення"}, "priceType": {"label": "Тип ціни", "options": {"fixed": {"label": "Фіксована", "hint": "Ціна варіанту доставки є фіксованою та не змінюється в залежності від вмісту замовлення."}, "calculated": {"label": "Обчислена", "hint": "Ціна варіанту доставки обчислюється постачальником виконання під час оформлення замовлення."}}}, "enableInStore": {"label": "Активувати в магазині", "hint": "Чи можуть клієнти використовувати цей варіант під час оформлення замовлення."}, "provider": "Постачальник виконання", "profile": "Профіль доставки"}}, "serviceZones": {"create": {"headerPickup": "Створити зону обслуговування для самовивозу з {{location}}", "headerShipping": "Створити зону обслуговування для доставки з {{location}}", "action": "Створити зону обслуговування", "successToast": "Зона обслуговування {{name}} була успішно створена."}, "edit": {"header": "Редагувати зону обслуговування", "successToast": "Зона обслуговування {{name}} була успішно оновлена."}, "delete": {"confirmation": "Ви збираєтеся видалити зону обслуговування {{name}}. Цю дію не можна скасувати.", "successToast": "Зона обслуговування {{name}} була успішно видалена."}, "manageAreas": {"header": "Керувати зонами для {{name}}", "action": "Керувати зонами", "label": "Зони", "hint": "Виберіть географічні зони, які покриває зона обслуговування.", "successToast": "Зони для {{name}} були успішно оновлені."}, "fields": {"noRecords": "Немає зон обслуговування для додавання варіантів доставки.", "tip": "Зона обслуговування — це набір географічних зон або територій. Вона використовується для обмеження доступних варіантів доставки до визначеного набору локацій."}}}, "shippingProfile": {"domain": "Профілі доставки", "subtitle": "Групуйте продукти з подібними вимогами до доставки в профілі.", "create": {"header": "Створити профіль доставки", "hint": "Створіть новий профіль доставки, щоб згрупувати продукти з подібними вимогами до доставки.", "successToast": "Профіль доставки {{name}} успішно створено."}, "delete": {"title": "Видалити профіль доставки", "description": "Ви збираєтеся видалити профіль доставки {{name}}. Цю дію неможливо скасувати.", "successToast": "Профіль доставки {{name}} успішно видалено."}, "tooltip": {"type": "Введіть тип профілю доставки, наприклад: В<PERSON><PERSON><PERSON><PERSON>, Надвеликий, Тільки вантаж, тощо."}}, "taxRegions": {"domain": "Податкові регіони", "list": {"hint": "Управляйте тим, що ви стягуєте з ваших клієнтів при покупках з різних країн та регіонів."}, "delete": {"confirmation": "Ви збираєтеся видалити податковий регіон. Цю дію неможливо скасувати.", "successToast": "Податковий регіон було успішно видалено."}, "create": {"header": "Створити податковий регіон", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретної країни.", "errors": {"rateIsRequired": "Податкова ставка є обов'язковою при створенні стандартної податкової ставки.", "nameIsRequired": "Назва є обов'язковою при створенні стандартної податкової ставки."}, "successToast": "Податковий регіон було успішно створено."}, "province": {"header": "Провінції", "create": {"header": "Створити податковий регіон провінції", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретної провінції."}}, "state": {"header": "Шта<PERSON>и", "create": {"header": "Створити податковий регіон штату", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного штату."}}, "stateOrTerritory": {"header": "Штати або Території", "create": {"header": "Створити податковий регіон штату/території", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного штату/території."}}, "county": {"header": "Округи", "create": {"header": "Створити податковий регіон округу", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного округу."}}, "region": {"header": "Регіони", "create": {"header": "Створити податковий регіон регіону", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного регіону."}}, "department": {"header": "Департаменти", "create": {"header": "Створити податковий регіон департаменту", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного департаменту."}}, "territory": {"header": "Терит<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Створити податковий регіон території", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретної території."}}, "prefecture": {"header": "Префектури", "create": {"header": "Створити податковий регіон префектури", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретної префектури."}}, "district": {"header": "Округи", "create": {"header": "Створити податковий регіон округу", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного округу."}}, "governorate": {"header": "Гу<PERSON><PERSON>рнаторства", "create": {"header": "Створити податковий регіон губернаторства", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного губернаторства."}}, "canton": {"header": "Кантони", "create": {"header": "Створити податковий регіон кантону", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного кантону."}}, "emirate": {"header": "Ем<PERSON><PERSON><PERSON><PERSON>и", "create": {"header": "Створити податковий регіон емірату", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного емірату."}}, "sublevel": {"header": "Підр<PERSON>вні", "create": {"header": "Створити податковий регіон підрівня", "hint": "Створіть новий податковий регіон, щоб визначити податкові ставки для конкретного підрівня."}}, "taxOverrides": {"header": "Перевизначення", "create": {"header": "Створити перевизначення", "hint": "Створіть податкову ставку, яка перевизначає стандартні податкові ставки для вибраних умов."}, "edit": {"header": "Редагувати перевизначення", "hint": "Редагуйте податкову ставку, що перевизначає стандартні податкові ставки для вибраних умов."}}, "taxRates": {"create": {"header": "Створити податкову ставку", "hint": "Створіть нову податкову ставку для визначення податкової ставки для регіону.", "successToast": "Податкова ставка була успішно створена."}, "edit": {"header": "Редагувати податкову ставку", "hint": "Редагуйте податкову ставку для визначення податкової ставки для регіону.", "successToast": "Податкова ставка була успішно оновлена."}, "delete": {"confirmation": "Ви збираєтеся видалити податкову ставку {{name}}. Цю дію неможливо скасувати.", "successToast": "Податкова ставка була успішно видалена."}}, "fields": {"isCombinable": {"label": "Можна комбінувати", "hint": "Чи можна поєднувати цю ставку податку з базовою ставкою з податкового регіону.", "true": "Можна комбінувати", "false": "Не можна комбінувати"}, "defaultTaxRate": {"label": "Базова ставка податку", "tooltip": "Базова ставка податку для цього регіону. Прикладом є стандартна ставка ПДВ для країни чи регіону.", "action": "Створити базову ставку податку"}, "taxRate": "Ставка податку", "taxCode": "Податковий код", "targets": {"label": "Цілі", "hint": "Оберіть цілі, до яких буде застосовуватися ця ставка податку.", "options": {"product": "Продукти", "productCollection": "Колекції продуктів", "productTag": "Теги продуктів", "productType": "Типи продуктів", "customerGroup": "Групи клієнтів"}, "operators": {"in": "в", "on": "на", "and": "і"}, "placeholders": {"product": "Шукати продукти", "productCollection": "Шукати колекції продуктів", "productTag": "Шукати теги продуктів", "productType": "Шукати типи продуктів", "customerGroup": "Шукати групи клієнтів"}, "tags": {"product": "Продукт", "productCollection": "Колекція продуктів", "productTag": "Тег продукту", "productType": "Тип продукту", "customerGroup": "Група клієнтів"}, "modal": {"header": "Додати цілі"}, "values_one": "{{count}} значення", "values_other": "{{count}} значень", "numberOfTargets_one": "{{count}} ціль", "numberOfTargets_other": "{{count}} цілей", "additionalValues_one": "і {{count}} додаткове значення", "additionalValues_other": "і {{count}} додаткових значень", "action": "Додати ціль"}, "sublevels": {"labels": {"province": "Провінція", "state": "<PERSON>т<PERSON><PERSON>", "region": "Регіон", "stateOrTerritory": "Штат/територія", "department": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "county": "Округ", "territory": "Територія", "prefecture": "Префектура", "district": "Рай<PERSON>н", "governorate": "Гу<PERSON><PERSON>рнаторство", "emirate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canton": "Кантон", "sublevel": "Код підрівня"}, "placeholders": {"province": "Оберіть провінцію", "state": "Об<PERSON><PERSON><PERSON><PERSON>ь штат", "region": "Оберіть регіон", "stateOrTerritory": "Оберіть штат/територію", "department": "Оберіть департамент", "county": "Обер<PERSON>ть округ", "territory": "Оберіть територію", "prefecture": "Оберіть префектуру", "district": "Оберіть район", "governorate": "Оберіть губернаторство", "emirate": "Обер<PERSON>ть емірат", "canton": "Оберіть кантон"}, "tooltips": {"sublevel": "Введіть ISO 3166-2 код для податкового підрівня.", "notPartOfCountry": "{{province}} не є частиною {{country}}. Перевірте, чи це правильно."}, "alert": {"header": "Підрівневі регіони вимкнені для цього податкового регіону", "description": "Підрівневі регіони за замовчуванням вимкнені для цього регіону. Ви можете їх увімкнути, щоб створити підрівневі регіони, такі як провінції, штати або території.", "action": "Увімкнути підрівневі регіони"}}, "noDefaultRate": {"label": "Без базової ставки", "tooltip": "Цей податковий регіон не має базової ставки податку. Якщо є стандартна ставка, така як ПДВ країни, будь ласка, додайте її до цього регіону."}}}, "promotions": {"domain": "Акції", "sections": {"details": "Деталі акції"}, "tabs": {"template": "Тип", "details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "campaign": "Кампанія"}, "fields": {"type": "Тип", "value_type": "Тип значення", "value": "Значення", "campaign": "Кампанія", "method": "Метод", "allocation": "Розподіл", "addCondition": "Додати умову", "clearAll": "Очистити все", "amount": {"tooltip": "Виберіть код валюти, щоб встановити суму"}, "conditions": {"rules": {"title": "Хто може використовувати цей код?", "description": "Який клієнт має право використовувати код акції? Код акції може використовувати будь-який клієнт, якщо він не був змінений."}, "target-rules": {"title": "До яких товарів буде застосовуватися акція?", "description": "Акція буде застосована до товарів, що відповідають наступним умовам."}, "buy-rules": {"title": "Що повинно бути в кошику, щоб активувати акцію?", "description": "Якщо ці умови виконуються, ми активуємо акцію для цільових товарів."}}}, "tooltips": {"campaignType": "Код валюти повинен бути вибраний в акції для встановлення бюджету витрат."}, "errors": {"requiredField": "Обов'язкове поле", "promotionTabError": "Виправте помилки на вкладці Акція перед продовженням"}, "toasts": {"promotionCreateSuccess": "Акція ({{code}}) була успішно створена."}, "create": {}, "edit": {"title": "Редагувати деталі акції", "rules": {"title": "Редагувати умови використання"}, "target-rules": {"title": "Редагувати умови товарів"}, "buy-rules": {"title": "Редагувати умови покупки"}}, "campaign": {"header": "Кампанія", "edit": {"header": "Редагувати кампанію", "successToast": "Кампанія акції успішно оновлена."}, "actions": {"goToCampaign": "Перейти до кампанії"}}, "campaign_currency": {"tooltip": "Це валюта акції. Змініть її на вкладці Деталі."}, "form": {"required": "Обов'язково", "and": "І", "selectAttribute": "Вибрати атрибут", "campaign": {"existing": {"title": "Існуюча кампанія", "description": "Додати акцію до існуючої кампанії.", "placeholder": {"title": "Немає існуючих кампаній", "desc": "Ви можете створити одну, щоб відслідковувати кілька акцій та встановлювати ліміти бюджету."}}, "new": {"title": "Нова кампанія", "description": "Створіть нову кампанію для цієї акції."}, "none": {"title": "Без кампанії", "description": "Продовжити без асоціації акції з кампанією"}}, "status": {"title": "Статус"}, "method": {"label": "Метод", "code": {"title": "<PERSON><PERSON><PERSON> а<PERSON>ції", "description": "Клієнти повинні ввести цей код під час оформлення замовлення"}, "automatic": {"title": "Автоматичний", "description": "Клієнти побачать цю акцію під час оформлення замовлення"}}, "max_quantity": {"title": "Макси<PERSON>а<PERSON>ьна кількість", "description": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість товарів, до яких застосовується ця акція."}, "type": {"standard": {"title": "Станд<PERSON><PERSON>тний", "description": "Стандартна акція"}, "buyget": {"title": "Купити - отримати", "description": "Акція купи X - отримай Y"}}, "allocation": {"each": {"title": "Кож<PERSON>н", "description": "Застосовує значення до кожного товару"}, "across": {"title": "По всьому", "description": "Застосовує значення до всіх товарів"}}, "code": {"title": "<PERSON>од", "description": "Код, який клієнти введуть під час оформлення замовлення."}, "value": {"title": "Значення акції"}, "value_type": {"fixed": {"title": "Значення акції", "description": "Сума, яка буде знижена. наприклад, 100"}, "percentage": {"title": "Значення акції", "description": "Відсоток знижки від суми. наприклад, 8%"}}}, "deleteWarning": "Ви збираєтеся видалити акцію {{code}}. Цю дію не можна скасувати.", "createPromotionTitle": "Створити акцію", "type": "<PERSON>и<PERSON> а<PERSON>", "conditions": {"add": "Додати умову", "list": {"noRecordsMessage": "Додайте умову для обмеження товарів, до яких застосовується акція."}}}, "campaigns": {"domain": "Кампанії", "details": "Детал<PERSON> кампанії", "status": {"active": "Активна", "expired": "Закінчилася", "scheduled": "Запланована"}, "delete": {"title": "Ви впевнені?", "description": "Ви збираєтесь видалити кампанію '{{name}}'. Цю дію неможливо скасувати.", "successToast": "Кампанія '{{name}}' була успішно створена."}, "edit": {"header": "Редагувати кампанію", "description": "Редагуйте деталі кампанії.", "successToast": "Кампанія '{{name}}' була успішно оновлена."}, "configuration": {"header": "Конфігурація", "edit": {"header": "Редагувати конфігурацію кампанії", "description": "Редагуйте конфігурацію кампанії.", "successToast": "Конфігурація кампанії була успішно оновлена."}}, "create": {"title": "Створити кампанію", "description": "Створіть рекламну кампанію.", "hint": "Створіть рекламну кампанію.", "header": "Створити кампанію", "successToast": "Кампанія '{{name}}' була успішно створена."}, "fields": {"name": "Назва", "identifier": "Ідентифікатор", "start_date": "Дата початку", "end_date": "Дата завершення", "total_spend": "Витрачений бюджет", "total_used": "Використаний бюджет", "budget_limit": "<PERSON>і<PERSON><PERSON>т бюджету", "campaign_id": {"hint": "У цьому списку відображаються лише кампанії з тим самим кодом валюти, що й у промоакції."}}, "budget": {"create": {"hint": "Створіть бюджет для кампанії.", "header": "Бюд<PERSON><PERSON><PERSON> кампанії"}, "details": "Бюд<PERSON><PERSON><PERSON> кампанії", "fields": {"type": "Тип", "currency": "Валюта", "limit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "used": "Використано"}, "type": {"spend": {"title": "Витрати", "description": "Встановіть ліміт на загальну суму знижок по всіх використаннях промоакції."}, "usage": {"title": "Використання", "description": "Встановіть ліміт на кількість використань промоакції."}}, "edit": {"header": "Редагувати бюджет кампанії"}}, "promotions": {"remove": {"title": "Видалити промоакцію з кампанії", "description": "Ви збираєтесь видалити {{count}} промоакцію(ї) з кампанії. Цю дію неможливо скасувати."}, "alreadyAdded": "Ця промоакція вже додана до кампанії.", "alreadyAddedDiffCampaign": "Ця промоакція вже додана до іншої кампанії ({{name}}).", "currencyMismatch": "Валюта промоакції та кампанії не збігаються", "toast": {"success": "Успішно додано {{count}} промоакцію(ї) до кампанії"}, "add": {"list": {"noRecordsMessage": "Створіть спочатку промоакцію."}}, "list": {"noRecordsMessage": "У кампанії немає промоакцій."}}, "deleteCampaignWarning": "Ви збираєтесь видалити кампанію {{name}}. Цю дію неможливо скасувати.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Цінові списки", "subtitle": "Створіть ціни для продажу або для конкретних умов.", "delete": {"confirmation": "Ви збираєтесь видалити ціновий список {{title}}. Цю дію неможливо скасувати.", "successToast": "Ціновий список {{title}} був успішно видалений."}, "create": {"header": "Створити ціновий список", "subheader": "Створіть новий ціновий список для управління цінами ваших продуктів.", "tabs": {"details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "products": "Продукти", "prices": "Ціни"}, "successToast": "Ціновий список {{title}} був успішно створений.", "products": {"list": {"noRecordsMessage": "Створіть спочатку продукт."}}}, "edit": {"header": "Редагувати ціновий список", "successToast": "Ціновий список {{title}} був успішно оновлений."}, "configuration": {"header": "Конфігурація", "edit": {"header": "Редагувати конфігурацію цінового списку", "description": "Редагуйте конфігурацію цінового списку.", "successToast": "Конфігурація цінового списку була успішно оновлена."}}, "products": {"header": "Продукти", "actions": {"addProducts": "Додати продукти", "editPrices": "Редагувати ціни"}, "delete": {"confirmation_one": "Ви збираєтесь видалити ціни для {{count}} продукту в ціновому списку. Цю дію неможливо скасувати.", "confirmation_other": "Ви збираєтесь видалити ціни для {{count}} продуктів у ціновому списку. Цю дію неможливо скасувати.", "successToast_one": "Успішно видалено ціни для {{count}} продукту.", "successToast_other": "Успішно видалено ціни для {{count}} продуктів."}, "add": {"successToast": "Ціни були успішно додані до цінового списку."}, "edit": {"successToast": "Ціни були успішно оновлені."}}, "fields": {"priceOverrides": {"label": "Перекриття цін", "header": "Перекриття цін"}, "status": {"label": "Статус", "options": {"active": "Активний", "draft": "Чернетка", "expired": "Закінчився", "scheduled": "Запланований"}}, "type": {"label": "Тип", "hint": "Оберіть тип цінового списку, який ви хочете створити.", "options": {"sale": {"label": "Розпродаж", "description": "Ціни для розпродажу — це тимчасові зміни цін для продуктів."}, "override": {"label": "Перекриття", "description": "Перекриття зазвичай використовуються для створення індивідуальних цін для клієнтів."}}}, "startsAt": {"label": "Ціновий список має дату початку?", "hint": "Заплануйте активацію цінового списку в майбутньому."}, "endsAt": {"label": "Ціновий список має дату завершення?", "hint": "Заплануйте деактивацію цінового списку в майбутньому."}, "customerAvailability": {"header": "Оберіть групи клієнтів", "label": "Доступність для клієнтів", "hint": "Обер<PERSON>ть, до яких груп клієнтів буде застосовуватися ціновий список.", "placeholder": "Шукайте групи клієнтів", "attribute": "Групи клієнтів"}}}, "profile": {"domain": "Профіль", "manageYourProfileDetails": "Керуйте деталями вашого профілю.", "fields": {"languageLabel": "Мова", "usageInsightsLabel": "Аналіз використання"}, "edit": {"header": "Редагувати профіль", "languageHint": "Мова, яку ви хочете використовувати в адміністративній панелі. Це не змінює мову вашого магазину.", "languagePlaceholder": "Вибе<PERSON><PERSON><PERSON>ь мову", "usageInsightsHint": "Поділіться аналізом використання і допоможіть нам покращити Medusa. Ви можете дізнатися більше про те, що ми збираємо і як ми це використовуємо в нашій <0>документації</0>."}, "toast": {"edit": "Зміни профілю збережено"}}, "users": {"domain": "Користувачі", "editUser": "Редагувати користувача", "inviteUser": "Запросити користувача", "inviteUserHint": "Запросіть нового користувача до вашого магазину.", "sendInvite": "Надіслати запрошення", "pendingInvites": "Очікують запрошення", "deleteInviteWarning": "Ви збираєтесь видалити запрошення для {{email}}. Цю дію неможливо скасувати.", "resendInvite": "Переслати запрошення", "copyInviteLink": "Копіювати посилання на запрошення", "expiredOnDate": "Термін дії закінчився {{date}}", "validFromUntil": "Дійсно з <0>{{from}}</0> по <1>{{until}}</1>", "acceptedOnDate": "Прийнято {{date}}", "inviteStatus": {"accepted": "Прийнято", "pending": "Очікує", "expired": "Термін дії закінчився"}, "roles": {"admin": "А<PERSON><PERSON><PERSON><PERSON>", "developer": "Розробник", "member": "<PERSON><PERSON><PERSON>н"}, "deleteUserWarning": "Ви збираєтесь видалити користувача {{name}}. Цю дію неможливо скасувати.", "invite": "Запросити"}, "store": {"domain": "Мага<PERSON>ин", "manageYourStoresDetails": "Керуйте деталями вашого магазину", "editStore": "Редагувати магазин", "defaultCurrency": "Основна валюта", "defaultRegion": "Основний регіон", "swapLinkTemplate": "Шаблон посилання для обміну", "paymentLinkTemplate": "Шаблон посилання для оплати", "inviteLinkTemplate": "Шаблон посилання для запрошення", "currencies": "Валюти", "addCurrencies": "Додати валюти", "enableTaxInclusivePricing": "Увімкнути ціноутворення з урахуванням податків", "disableTaxInclusivePricing": "Вимкнути ціноутворення з урахуванням податків", "removeCurrencyWarning_one": "Ви збираєтесь видалити {{count}} валюту з вашого магазину. Переконайтеся, що ви видалили всі ціни, що використовують цю валюту, перед тим як продовжити.", "removeCurrencyWarning_other": "Ви збираєтесь видалити {{count}} валют з вашого магазину. Переконайтеся, що ви видалили всі ціни, що використовують ці валюти, перед тим як продовжити.", "currencyAlreadyAdded": "Валюта вже додана до вашого магазину.", "edit": {"header": "Редагувати магазин"}, "toast": {"update": "Магазин успішно оновлено", "currenciesUpdated": "Валюти успішно оновлені", "currenciesRemoved": "Валюти успішно видалено з магазину", "updatedTaxInclusivitySuccessfully": "Ціноутворення з урахуванням податків успішно оновлено"}}, "regions": {"domain": "Регіони", "subtitle": "Регіон — це територія, на якій ви продаєте продукцію. Він може охоплювати кілька країн та мати різні ставки податків, постачальників і валюту.", "createRegion": "Створити регіон", "createRegionHint": "Керуйте ставками податків і постачальниками для групи країн.", "addCountries": "Додати країни", "editRegion": "Редагувати регіон", "countriesHint": "Додайте країни, що входять до цього регіону.", "deleteRegionWarning": "Ви збираєтесь видалити регіон {{name}}. Цю дію неможливо скасувати.", "removeCountriesWarning_one": "Ви збираєтесь видалити {{count}} країну з регіону. Цю дію неможливо скасувати.", "removeCountriesWarning_other": "Ви збираєтесь видалити {{count}} країн з регіону. Цю дію неможливо скасувати.", "removeCountryWarning": "Ви збираєтесь видалити країну {{name}} з регіону. Цю дію неможливо скасувати.", "automaticTaxesHint": "Коли увімкнено, податки обчислюються тільки на основі адреси доставки під час оформлення замовлення.", "taxInclusiveHint": "Коли увімкнено, ціни в регіоні будуть включати податки.", "providersHint": "Додайте постачальників платіжних послуг для цього регіону.", "shippingOptions": "Опції доставки", "deleteShippingOptionWarning": "Ви збираєтесь видалити опцію доставки {{name}}. Цю дію неможливо скасувати.", "return": "Повернення", "outbound": "Вихідні", "priceType": "Тип ціни", "flatRate": "Фіксована ставка", "calculated": "Обчислена", "list": {"noRecordsMessage": "Створіть регіон для територій, на яких ви продаєте продукцію."}, "toast": {"delete": "Регіон успішно видалено", "edit": "Зміни в регіоні збережено", "create": "Регіон успішно створено", "countries": "Країни регіону успішно оновлені"}, "shippingOption": {"createShippingOption": "Створити опцію доставки", "createShippingOptionHint": "Створіть нову опцію доставки для регіону.", "editShippingOption": "Редагувати опцію доставки", "fulfillmentMethod": "Метод виконання", "type": {"outbound": "Вихідний", "outboundHint": "Використовуйте це, якщо створюєте опцію доставки для відправки товарів клієнту.", "return": "Повернення", "returnHint": "Використовуйте це, якщо створюєте опцію доставки для повернення товарів від клієнта."}, "priceType": {"label": "Тип ціни", "flatRate": "Фіксована ставка", "calculated": "Обчислена"}, "availability": {"adminOnly": "Тільки для адміністраторів", "adminOnlyHint": "Коли увімкнено, опція доставки буде доступна тільки в адміністративній панелі, а не в магазині."}, "taxInclusiveHint": "Коли увімкнено, ціна опції доставки буде включати податки.", "requirements": {"label": "Вимоги", "hint": "Вкажіть вимоги для опції доставки."}}}, "taxes": {"domain": "Податкові регіони", "domainDescription": "Керуйте вашим податковим регіоном", "countries": {"taxCountriesHint": "Налаштування податків застосовуються до вказаних країн."}, "settings": {"editTaxSettings": "Редагувати налаштування податків", "taxProviderLabel": "Постачальник податків", "systemTaxProviderLabel": "Системний постачальник податків", "calculateTaxesAutomaticallyLabel": "Автоматичне обчислення податків", "calculateTaxesAutomaticallyHint": "Коли увімкнено, ставки податків будуть обчислюватися автоматично і застосовуватися до кошиків. Коли вимкнено, податки потрібно вручну обчислювати під час оформлення замовлення. Ручні податки рекомендуються для використання з постачальниками податків третьої сторони.", "applyTaxesOnGiftCardsLabel": "Застосувати податки до подарункових карт", "applyTaxesOnGiftCardsHint": "Коли увімкнено, податки будуть застосовуватися до подарункових карт під час оформлення замовлення. У деяких країнах податкові правила вимагають застосування податків до подарункових карт під час покупки.", "defaultTaxRateLabel": "Стандартна ставка податку", "defaultTaxCodeLabel": "Стандартний податковий код"}, "defaultRate": {"sectionTitle": "Стандартна ставка податку"}, "taxRate": {"sectionTitle": "Ставки податку", "createTaxRate": "Створити ставку податку", "createTaxRateHint": "Створіть нову ставку податку для регіону.", "deleteRateDescription": "Ви збираєтесь видалити ставку податку {{name}}. Цю дію неможливо скасувати.", "editTaxRate": "Редагувати ставку податку", "editRateAction": "Редагувати ставку", "editOverridesAction": "Редагувати переваги", "editOverridesTitle": "Редагувати переваги ставки податку", "editOverridesHint": "Вкажіть переваги для ставки податку.", "deleteTaxRateWarning": "Ви збираєтесь видалити ставку податку {{name}}. Цю дію неможливо скасувати.", "productOverridesLabel": "Переваги для товарів", "productOverridesHint": "Вкажіть переваги для товарів щодо ставки податку.", "addProductOverridesAction": "Додати переваги для товарів", "productTypeOverridesLabel": "Переваги для типів товарів", "productTypeOverridesHint": "Вкажіть переваги для типів товарів щодо ставки податку.", "addProductTypeOverridesAction": "Додати переваги для типів товарів", "shippingOptionOverridesLabel": "Переваги для опцій доставки", "shippingOptionOverridesHint": "Вкажіть переваги для опцій доставки щодо ставки податку.", "addShippingOptionOverridesAction": "Додати переваги для опцій доставки", "productOverridesHeader": "Товари", "productTypeOverridesHeader": "Типи товарів", "shippingOptionOverridesHeader": "Опції доставки"}}, "locations": {"domain": "Локації", "editLocation": "Редагувати локацію", "addSalesChannels": "Додати канали продажу", "noLocationsFound": "Локації не знайдено", "selectLocations": "Виберіть локації, де є товар.", "deleteLocationWarning": "Ви збираєтесь видалити локацію {{name}}. Цю дію не можна скасувати.", "removeSalesChannelsWarning_one": "Ви збираєтесь видалити {{count}} канал продажу з локації.", "removeSalesChannelsWarning_other": "Ви збираєтесь видалити {{count}} канали продажу з локації.", "toast": {"create": "Локацію створено успішно", "update": "Локацію оновлено успішно", "removeChannel": "Канал продажу успішно видалено"}}, "reservations": {"domain": "Резервац<PERSON><PERSON>", "subtitle": "Керуйте зарезервованою кількістю товарів на складі.", "deleteWarning": "Ви збираєтесь видалити резервацію. Цю дію неможливо скасувати."}, "salesChannels": {"domain": "Канали продажу", "subtitle": "Керуйте онлайн та офлайн каналами, на яких ви продаєте товари.", "createSalesChannel": "Створити канал продажу", "createSalesChannelHint": "Створіть новий канал продажу для продажу ваших товарів.", "enabledHint": "Вкаж<PERSON>ть, чи активований канал продажу.", "removeProductsWarning_one": "Ви збираєтесь видалити {{count}} товар з {{sales_channel}}.", "removeProductsWarning_other": "Ви збираєтесь видалити {{count}} товари з {{sales_channel}}.", "addProducts": "Додати товари", "editSalesChannel": "Редагувати канал продажу", "productAlreadyAdded": "Товар вже доданий до каналу продажу.", "deleteSalesChannelWarning": "Ви збираєтесь видалити канал продажу {{name}}. Цю дію неможливо скасувати.", "toast": {"create": "Канал продажу успішно створено", "update": "Канал продажу успішно оновлено", "delete": "Канал продажу успішно видалено"}, "tooltip": {"cannotDeleteDefault": "Не можна видалити канал продажу за замовчуванням"}, "products": {"list": {"noRecordsMessage": "У каналі продажу немає товарів."}, "add": {"list": {"noRecordsMessage": "Спочатку створіть товар."}}}}, "apiKeyManagement": {"domain": {"publishable": "Публічні API ключі", "secret": "Секретні API ключі"}, "subtitle": {"publishable": "Керуйте API ключами, які використовуються в магазині для обмеження запитів до конкретних каналів продажу.", "secret": "Керуйте API ключами, які використовуються для автентифікації адміністраторів у адміністративних додатках."}, "status": {"active": "Активний", "revoked": "Анульований"}, "type": {"publishable": "Публічний", "secret": "Секретний"}, "create": {"createPublishableHeader": "Створити публічний API ключ", "createPublishableHint": "Створіть новий публічний API ключ для обмеження запитів до конкретних каналів продажу.", "createSecretHeader": "Створити секретний API ключ", "createSecretHint": "Створіть новий секретний API ключ для доступу до Medusa API як автентифікований адміністратор.", "secretKeyCreatedHeader": "Секретний ключ створено", "secretKeyCreatedHint": "Ваш новий секретний ключ створено. Скопіюйте та збережіть його зараз. Це єдиний раз, коли він буде показаний.", "copySecretTokenSuccess": "Секретний ключ скопійовано в буфер обміну.", "copySecretTokenFailure": "Не вдалося скопіювати секретний ключ в буфер обміну.", "successToast": "API ключ успішно створено."}, "edit": {"header": "Редагувати API ключ", "description": "Редагуйте заголовок API ключа.", "successToast": "API ключ {{title}} успішно оновлено."}, "salesChannels": {"title": "Додати канали продажу", "description": "Додайте канали продажу, до яких буде обмежено доступ для API ключа.", "successToast_one": "{{count}} канал продажу успішно додано до API ключа.", "successToast_other": "{{count}} канали продажу успішно додано до API ключа.", "alreadyAddedTooltip": "Канал продажу вже додано до API ключа.", "list": {"noRecordsMessage": "У публічного API ключа немає каналів продажу."}}, "delete": {"warning": "Ви збираєтесь видали<PERSON><PERSON> <PERSON> ключ {{title}}. Цю дію неможливо скасувати.", "successToast": "API ключ {{title}} успішно видалено."}, "revoke": {"warning": "Ви збираєтесь анулю<PERSON>а<PERSON><PERSON> <PERSON> ключ {{title}}. Цю дію неможливо скасувати.", "successToast": "API ключ {{title}} успішно анульовано."}, "addSalesChannels": {"list": {"noRecordsMessage": "Спочатку створіть канал продажу."}}, "removeSalesChannel": {"warning": "Ви збираєтесь видалити канал продажу {{name}} з API ключа. Цю дію неможливо скасувати.", "warningBatch_one": "Ви збираєтесь видалити {{count}} канал продажу з API ключа. Цю дію неможливо скасувати.", "warningBatch_other": "Ви збираєтесь видалити {{count}} канали продажу з API ключа. Цю дію неможливо скасувати.", "successToast": "Канал продажу успішно видалено з API ключа.", "successToastBatch_one": "{{count}} канал продажу успішно видалено з API ключа.", "successToastBatch_other": "{{count}} канали продажу успішно видалено з API ключа."}, "actions": {"revoke": "Анулюва<PERSON>и <PERSON> ключ", "copy": "Копіюва<PERSON>и <PERSON> ключ", "copySuccessToast": "API ключ скопійовано в буфер обміну."}, "table": {"lastUsedAtHeader": "Останнє використання", "createdAtHeader": "Створено"}, "fields": {"lastUsedAtLabel": "Останнє використання", "revokedByLabel": "Ан<PERSON><PERSON><PERSON><PERSON><PERSON>в", "revokedAtLabel": "Анулювано", "createdByLabel": "Створено"}}, "returnReasons": {"domain": "Причини повернення", "subtitle": "Керуйте причинами повернення товарів.", "calloutHint": "Керуйте причинами для категоризації повернень.", "editReason": "Редагувати причину повернення", "create": {"header": "Додати причину повернення", "subtitle": "Вкажіть найбільш поширені причини повернення.", "hint": "Створіть нову причину повернення для категоризації повернень.", "successToast": "Причина повернення {{label}} успішно створена."}, "edit": {"header": "Редагувати причину повернення", "subtitle": "Редагуйте значення причини повернення.", "successToast": "Причина повернення {{label}} успішно оновлена."}, "delete": {"confirmation": "Ви збираєтесь видалити причину повернення {{label}}. Цю дію неможливо скасувати.", "successToast": "Причина повернення {{label}} успішно видалена."}, "fields": {"value": {"label": "Значення", "placeholder": "неправильний_розмір", "tooltip": "Значення має бути унікальним ідентифікатором для причини повернення."}, "label": {"label": "Мітка", "placeholder": "Неправильний розмір"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Клієнт отримав неправильний розмір"}}}, "login": {"forgotPassword": "Забули пароль? - <0>Скинути</0>", "title": "Ласкаво просимо до Medusa", "hint": "Увійдіть, щоб отримати доступ до особистого кабінету"}, "invite": {"title": "Ласкаво просимо до Medusa", "hint": "Створіть акаунт нижче", "backToLogin": "Повернутися до входу", "createAccount": "Створити акаунт", "alreadyHaveAccount": "Вже маєте акаунт? - <0>Увійти</0>", "emailTooltip": "Вашу електронну пошту не можна змінити. Якщо ви хочете використовувати іншу електронну пошту, потрібно надіслати нове запрошення.", "invalidInvite": "Запрошення недійсне або сплив термін.", "successTitle": "Ваш акаунт зареєстровано", "successHint": "Розпочніть роботу з Medusa Admin прямо зараз.", "successAction": "Почати роботу з Medusa Admin", "invalidTokenTitle": "Ваш токен запрошення недійсний", "invalidTokenHint": "Спробуйте запросити нове посилання для запрошення.", "passwordMismatch": "Паролі не збігаються", "toast": {"accepted": "Запрошення успішно прийняте"}}, "resetPassword": {"title": "Скинути пароль", "hint": "Введіть свою електронну пошту, і ми надішлемо вам інструкції щодо скидання пароля.", "email": "Електронна пошта", "sendResetInstructions": "Надіслати інструкції для скидання", "backToLogin": "<0>Повернутися до входу</0>", "newPasswordHint": "Виберіть новий пароль нижче.", "invalidTokenTitle": "Ваш токен скидання пароля недійсний", "invalidTokenHint": "Спробуйте запитати нове посилання для скидання пароля.", "expiredTokenTitle": "Ваш токен скидання пароля сплив", "goToResetPassword": "Перейти до скидання пароля", "resetPassword": "Скинути пароль", "newPassword": "Новий пароль", "repeatNewPassword": "Повторіть новий пароль", "tokenExpiresIn": "Токен спливає через <0>{{time}}</0> хвилин", "successfulRequestTitle": "Успішно надіслано вам лист", "successfulRequest": "Ми надіслали вам електронний лист, який ви можете використати для скидання пароля. Перевірте папку спаму, якщо не отримали його через кілька хвилин.", "successfulResetTitle": "Пароль успішно скинуто", "successfulReset": "Будь ласка, увійдіть на сторінці входу.", "passwordMismatch": "Паролі не збігаються", "invalidLinkTitle": "Ваше посилання для скидання пароля недійсне", "invalidLinkHint": "Спробуйте ще раз скинути пароль."}, "workflowExecutions": {"domain": "Процеси роботи", "subtitle": "Переглядайте та відстежуйте виконання процесів роботи у вашій Medusa аплікації.", "transactionIdLabel": "ID транзакції", "workflowIdLabel": "ID процесу роботи", "progressLabel": "Прогрес", "stepsCompletedLabel_one": "{{completed}} з {{count}} кроку", "stepsCompletedLabel_other": "{{completed}} з {{count}} кроків", "list": {"noRecordsMessage": "Немає виконаних процесів роботи."}, "history": {"sectionTitle": "Історія", "runningState": "Виконується...", "awaitingState": "Очікує", "failedState": "Не вдалося", "skippedState": "Пропущено", "skippedFailureState": "Пропущено (Помилка)", "definitionLabel": "<PERSON><PERSON><PERSON><PERSON>", "outputLabel": "Виведення", "compensateInputLabel": "Компенсація введення", "revertedLabel": "Скасовано", "errorLabel": "Помилка"}, "state": {"done": "Завершено", "failed": "Не вдалося", "reverted": "Скасовано", "invoking": "Викликається", "compensating": "Компенсується", "notStarted": "Не розпочато"}, "transaction": {"state": {"waitingToCompensate": "Очікування компенсації"}}, "step": {"state": {"skipped": "Пропущено", "skippedFailure": "Пропущено (Помилка)", "dormant": "Неактивно", "timeout": "<PERSON><PERSON><PERSON> вий<PERSON>ов"}}}, "productTypes": {"domain": "Типи продуктів", "subtitle": "Організуйте свої продукти за типами.", "create": {"header": "Створити тип продукту", "hint": "Створіть новий тип продукту для категоризації ваших продуктів.", "successToast": "Тип продукту {{value}} успішно створено."}, "edit": {"header": "Редагувати тип продукту", "successToast": "Тип продукту {{value}} успішно оновлено."}, "delete": {"confirmation": "Ви збираєтеся видалити тип продукту {{value}}. Цю дію не можна скасувати.", "successToast": "Тип продукту {{value}} успішно видалено."}, "fields": {"value": "Значення"}}, "productTags": {"domain": "Теги продуктів", "create": {"header": "Створити тег продукту", "subtitle": "Створіть новий тег продукту для категоризації ваших продуктів.", "successToast": "Тег продукту {{value}} успішно створено."}, "edit": {"header": "Редагувати тег продукту", "subtitle": "Редагуйте значення тегу продукту.", "successToast": "Тег продукту {{value}} успішно оновлено."}, "delete": {"confirmation": "Ви збираєтеся видалити тег продукту {{value}}. Цю дію не можна скасувати.", "successToast": "Тег продукту {{value}} успішно видалено."}, "fields": {"value": "Значення"}}, "notifications": {"domain": "Сповіщення", "emptyState": {"title": "Немає сповіщень", "description": "У вас наразі немає сповіщень, але коли вони з'являться, вони будуть відображені тут."}, "accessibility": {"description": "Сповіщення про активність Medusa будуть відображатися тут."}}, "errors": {"serverError": "Помилка сервера - Спробуйте ще раз пізніше.", "invalidCredentials": "Невірний email або пароль"}, "statuses": {"scheduled": "Заплановано", "expired": "Тер<PERSON><PERSON>н минув", "active": "Активно", "enabled": "Включено", "disabled": "Вимкнено"}, "labels": {"productVariant": "Варіант продукту", "prices": "Ціни", "available": "Доступно", "inStock": "В наявності", "added": "Додано", "removed": "Видалено", "from": "З", "to": "До"}, "fields": {"amount": "Сума", "refundAmount": "Сума повернення", "name": "Ім'я", "default": "За замовчуванням", "lastName": "Прізвище", "firstName": "Ім'я", "title": "Назва", "customTitle": "Користувацька назва", "manageInventory": "Керування запасами", "inventoryKit": "Має комплект запасів", "inventoryItems": "Товари на складі", "inventoryItem": "Товар на складі", "requiredQuantity": "Необх<PERSON>дна кількість", "description": "<PERSON><PERSON><PERSON><PERSON>", "email": "Електронна пошта", "password": "Пароль", "repeatPassword": "Повторіть пароль", "confirmPassword": "Підтвердьте пароль", "newPassword": "Новий пароль", "repeatNewPassword": "Повторіть новий пароль", "categories": "Категорії", "shippingMethod": "Метод доставки", "configurations": "Конфігураці<PERSON>", "conditions": "Умови", "category": "Категорія", "collection": "Колекція", "discountable": "Підлягає знижці", "handle": "Обробка", "subtitle": "Підзаголовок", "by": "<PERSON>ід", "item": "<PERSON><PERSON><PERSON><PERSON>", "qty": "к-сть", "limit": "Обмеження", "tags": "Теги", "type": "Тип", "reason": "Причина", "none": "немає", "all": "всі", "search": "По<PERSON><PERSON>к", "percentage": "Відсоток", "sales_channels": "Канали продажу", "customer_groups": "Групи клієнтів", "product_tags": "Теги товарів", "product_types": "Типи товарів", "product_collections": "Колекції товарів", "status": "Статус", "code": "<PERSON>од", "value": "Значення", "disabled": "Вимкнено", "dynamic": "Д<PERSON><PERSON><PERSON><PERSON>чний", "normal": "Звичайний", "years": "Роки", "months": "Місяці", "days": "<PERSON><PERSON><PERSON>", "hours": "Годи<PERSON>и", "minutes": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalRedemptions": "Загальна кількість викупів", "countries": "Країни", "paymentProviders": "Постачальники платежів", "refundReason": "Причина повернення", "fulfillmentProviders": "Постачальники виконання", "fulfillmentProvider": "Постачальник виконання", "providers": "Постачальники", "availability": "Наявність", "inventory": "Запаси", "optional": "Необов'язково", "note": "Примітка", "automaticTaxes": "Автоматичні податки", "taxInclusivePricing": "Ціни з урахуванням податку", "currency": "Валюта", "address": "Адреса", "address2": "Квартира, офіс тощо.", "city": "Місто", "postalCode": "Поштовий індекс", "country": "Країна", "state": "<PERSON>т<PERSON><PERSON>", "province": "Область", "company": "Компанія", "phone": "Телефон", "metadata": "Метадані", "selectCountry": "Виберіть країну", "products": "Товари", "variants": "Варіанти", "orders": "Замовлення", "account": "Обліковий запис", "total": "Загальна сума замовлення", "paidTotal": "Загальна сума оплати", "totalExclTax": "Загальна сума без податку", "subtotal": "Підсумок", "shipping": "Доставка", "outboundShipping": "Вихідна доставка", "returnShipping": "Повернення доставки", "tax": "Податок", "created": "Створено", "key": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Клієнт", "date": "Дата", "order": "Замовлення", "fulfillment": "Виконання", "provider": "Постачальник", "payment": "Оплата", "items": "Товари", "salesChannel": "Канал продажу", "region": "Регіон", "discount": "Знижка", "role": "Роль", "sent": "Надіслано", "salesChannels": "Канали продажу", "product": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Створено", "updatedAt": "Оновлено", "revokedAt": "Анульовано", "true": "Правда", "false": "Неправда", "giftCard": "Подарункова картка", "tag": "Тег", "dateIssued": "Дата видачі", "issuedDate": "Дата видачі", "expiryDate": "Дата закінчення терміну дії", "price": "Ціна", "priceTemplate": "Ціна {{regionOrCurrency}}", "height": "Висота", "width": "Ши<PERSON><PERSON><PERSON>", "length": "Довжина", "weight": "Вага", "midCode": "MID код", "hsCode": "HS код", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Кількість на складі", "barcode": "Штрихкод", "countryOfOrigin": "Країна виробництва", "material": "Ма<PERSON><PERSON><PERSON><PERSON><PERSON>л", "thumbnail": "Мініатюра", "sku": "SKU", "managedInventory": "Керовані запаси", "allowBackorder": "Дозволити передзамовлення", "inStock": "В наявності", "location": "Локація", "quantity": "Кількість", "variant": "Вар<PERSON><PERSON><PERSON>т", "id": "ID", "parent": "Батьківський", "minSubtotal": "Мін. підсумок", "maxSubtotal": "Макс. підсумок", "shippingProfile": "Профіль доставки", "summary": "Резюме", "details": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "label": "Мітка", "rate": "Ставка", "requiresShipping": "Потрібна доставка", "unitPrice": "Ціна за одиницю", "startDate": "Дата початку", "endDate": "Дата закінчення", "draft": "Чернетка", "values": "Значення"}, "quotes": {"domain": "Котирування", "title": "Котирування", "subtitle": "Керування котируваннями та пропозиціями клієнтів", "noQuotes": "Котирувань не знайдено", "noQuotesDescription": "Наразі котирувань немає. Створіть одне з магазину.", "table": {"id": "ID котирування", "customer": "Клієнт", "status": "Статус", "company": "Компанія", "amount": "Сума", "createdAt": "Створено", "updatedAt": "Оновлено", "actions": "Дії"}, "status": {"pending_merchant": "Очікує торговця", "pending_customer": "Очікує клієнта", "merchant_rejected": "Відхилено торговцем", "customer_rejected": "Відхилено клієнтом", "accepted": "Прийнято", "unknown": "Невідомо"}, "actions": {"sendQuote": "Надіслати котирування", "rejectQuote": "Відхилити котирування", "viewOrder": "Переглянути замовлення"}, "details": {"header": "Деталі котирування", "quoteSummary": "Резюме котирування", "customer": "Клієнт", "company": "Компанія", "items": "Товари", "total": "Загалом", "subtotal": "Підсумок", "shipping": "Доставка", "tax": "Податок", "discounts": "Знижки", "originalTotal": "Початкова сума", "quoteTotal": "Сума котирування", "messages": "Повідомлення", "actions": "Дії", "sendMessage": "Надіслати повідомлення", "send": "Надіслати", "pickQuoteItem": "Вибрати товар котирування", "selectQuoteItem": "Виберіть товар котирування для коментаря", "selectItem": "Вибрати товар", "manage": "Керувати", "phone": "Телефон", "spendingLimit": "<PERSON>і<PERSON><PERSON>т витрат", "name": "Ім'я", "manageQuote": "Керувати котируванням", "noItems": "У цьому котируванні немає товарів", "noMessages": "Для цього котирування немає повідомлень"}, "items": {"title": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "Кількість", "unitPrice": "Ціна за одиницю", "total": "Загалом"}, "messages": {"admin": "Адміністратор", "customer": "Клієнт", "placeholder": "Введіть ваше повідомлення тут..."}, "filters": {"status": "Фільтрувати за статусом"}, "confirmations": {"sendTitle": "Надіслати котирування", "sendDescription": "Ви впевнені, що хочете надіслати це котирування клієнту?", "rejectTitle": "Відхилити котирування", "rejectDescription": "Ви впевнені, що хочете відхилити це котирування?"}, "acceptance": {"message": "Котирування було прийнято"}, "toasts": {"sendSuccess": "Котирування успішно надіслано клієнту", "sendError": "Не вдалося надіслати котирування", "rejectSuccess": "Котирування клієнта успішно відхилено", "rejectError": "Не вдалося відхилити котирування", "messageSuccess": "Повідомлення успішно надіслано клієнту", "messageError": "Не вдалося надіслати повідомлення", "updateSuccess": "Котирування успішно оновлено"}, "manage": {"overridePriceHint": "Перевизначити початкову ціну для цього товару", "updatePrice": "Оновити ціну"}}, "companies": {"domain": "Компанії", "title": "Компанії", "subtitle": "Керування бізнес-відносинами", "noCompanies": "Компаній не знайдено", "noCompaniesDescription": "Створіть свою першу компанію для початку.", "notFound": "Компанію не знайдено", "table": {"name": "Назва", "phone": "Телефон", "email": "Електронна пошта", "address": "Адреса", "employees": "Співробітники", "customerGroup": "Група клієнтів", "actions": "Дії"}, "fields": {"name": "Назва компанії", "email": "Електронна пошта", "phone": "Телефон", "website": "Веб-сайт", "address": "Адреса", "city": "Місто", "state": "Область", "zip": "Поштовий індекс", "zipCode": "Поштовий індекс", "country": "Країна", "currency": "Валюта", "logoUrl": "URL логотипу", "description": "<PERSON><PERSON><PERSON><PERSON>", "employees": "Співробітники", "customerGroup": "Група клієнтів", "approvalSettings": "Налаштування затвердження"}, "placeholders": {"name": "Введіть назву компанії", "email": "Введіть адресу електронної пошти", "phone": "Введіть номер телефону", "website": "Введіть URL веб-сайту", "address": "Введіть адресу", "city": "Введіть місто", "state": "Введіть область", "zip": "Введіть поштовий індекс", "logoUrl": "Введіть URL логотипу", "description": "Введіть опис компанії", "selectCountry": "Виберіть країну", "selectCurrency": "Виберіть валюту"}, "validation": {"nameRequired": "Назва компанії є обов'язковою", "emailRequired": "Електронна пошта є обов'язковою", "emailInvalid": "Недійсна адреса електронної пошти", "addressRequired": "Адреса є обов'язковою", "cityRequired": "Місто є обов'язковим", "stateRequired": "Область є обов'язковою", "zipRequired": "Поштовий індекс є обов'язковим"}, "create": {"title": "Створити компанію", "description": "Створіть нову компанію для керування бізнес-відносинами.", "submit": "Створити компанію"}, "edit": {"title": "Редагувати компанію", "submit": "Оновити компанію"}, "details": {"actions": "Дії"}, "approvals": {"requiresAdminApproval": "Потребує затвердження адміністратора", "requiresSalesManagerApproval": "Потребує затвердження менеджера з продажу", "noApprovalRequired": "Затвердження не потрібне"}, "deleteWarning": "Це назавжди видалить компанію та всі пов'язані дані.", "approvalSettings": {"title": "Налаштування затвердження", "requiresAdminApproval": "Потребує затвердження адміністратора", "requiresSalesManagerApproval": "Потребує затвердження менеджера з продажу", "requiresAdminApprovalDesc": "Замовлення від цієї компанії потребують затвердження адміністратора перед обробкою", "requiresSalesManagerApprovalDesc": "Замовлення від цієї компанії потребують затвердження менеджера з продажу перед обробкою", "updateSuccess": "Налаштування затвердження успішно оновлено", "updateError": "Не вдалося оновити налаштування затвердження"}, "customerGroup": {"name": "Назва групи клієнтів", "description": "Керування групами клієнтів для цієї компанії", "noGroups": "Немає доступних груп клієнтів", "addSuccess": "Компанію успішно додано до групи клієнтів", "addError": "Не вдалося додати компанію до групи клієнтів", "removeSuccess": "Компанію успішно видалено з групи клієнтів", "removeError": "Не вдалося видалити компанію з групи клієнтів"}, "actions": {"edit": "Редагувати компанію", "editDetails": "Редагувати деталі", "manageCustomerGroup": "Керувати групою клієнтів", "approvalSettings": "Налаштування затвердження", "delete": "Видалити компанію", "confirmDelete": "Підтвердити видалення"}, "delete": {"title": "Видалити компанію", "description": "Ви впевнені, що хочете видалити цю компанію? Цю дію неможливо скасувати."}, "employees": {"title": "Співробітники", "noEmployees": "Для цієї компанії не знайдено співробітників", "name": "Ім'я", "email": "Електронна пошта", "phone": "Телефон", "role": "Роль", "spendingLimit": "<PERSON>і<PERSON><PERSON>т витрат", "admin": "Адміністратор", "employee": "Співробітник", "add": "Додати співробітника", "create": {"title": "Створити співробітника", "success": "Співробітника успішно створено", "error": "Не вдалося створити співробітника"}, "form": {"details": "Детальна інформація", "permissions": "Дозволи", "firstName": "Ім'я", "lastName": "Прізвище", "email": "Електронна пошта", "phone": "Телефон", "spendingLimit": "<PERSON>і<PERSON><PERSON>т витрат", "adminAccess": "Доступ адміністратора", "isAdmin": "Є адміністратором", "isAdminDesc": "Надати привілеї адміністратора цьому співробітнику", "isAdminTooltip": "Адміністратори можуть керувати налаштуваннями компанії та іншими співробітниками", "firstNamePlaceholder": "Введіть ім'я", "lastNamePlaceholder": "Введіть прізвище", "emailPlaceholder": "Введіть адресу електронної пошти", "phonePlaceholder": "Введіть номер телефону", "spendingLimitPlaceholder": "Введіть ліміт витрат", "save": "Зберегти", "saving": "Збереження..."}, "delete": {"confirmation": "Ви впевнені, що хочете видалити цього співробітника?", "success": "Співробітника успішно видалено"}, "edit": {"title": "Редагувати співробітника"}, "toasts": {"updateSuccess": "Співробітника успішно оновлено", "updateError": "Не вдалося оновити співробітника"}}, "toasts": {"createSuccess": "Компанію успішно створено", "createError": "Не вдалося створити компанію", "updateSuccess": "Компанію успішно оновлено", "updateError": "Не вдалося оновити компанію", "deleteSuccess": "Компанію успішно видалено", "deleteError": "Не вдалося видалити компанію"}}, "approvals": {"domain": "Затвердження", "title": "Затвердження", "subtitle": "Керування робочими процесами затвердження", "noApprovals": "Затверджень не знайдено", "noApprovalsDescription": "Наразі немає затверджень для перегляду.", "table": {"id": "ID", "type": "Тип", "company": "Компанія", "customer": "Клієнт", "amount": "Сума", "status": "Статус", "createdAt": "Створено"}, "status": {"pending": "Очікує", "approved": "Затверджено", "rejected": "Від<PERSON>илено", "expired": "Прострочено", "unknown": "Невідомо"}, "details": {"header": "Деталі затвердження", "summary": "Підсумок затвердження", "company": "Компанія", "customer": "Клієнт", "order": "Замовлення", "amount": "Сума", "updatedAt": "Оновлено", "reason": "Причина", "actions": "Дії"}, "actions": {"approve": "Затвердити", "reject": "Від<PERSON><PERSON><PERSON>ити", "confirmApprove": "Підтвердити затвердження", "confirmReject": "Підтвердити відхилення", "reasonPlaceholder": "Введіть причину (необов'язково)..."}, "filters": {"status": "Фільтрувати за статусом"}, "toasts": {"approveSuccess": "Успішно затверджено", "approveError": "Не вдалося затвердити", "rejectSuccess": "Успішно відхилено", "rejectError": "Не вдалося відхилити"}}, "dateTime": {"years_one": "<PERSON><PERSON><PERSON>", "years_other": "Роки", "months_one": "Місяць", "months_other": "Місяці", "weeks_one": "Тиждень", "weeks_other": "Тиж<PERSON><PERSON>", "days_one": "День", "days_other": "<PERSON><PERSON><PERSON>", "hours_one": "Год<PERSON><PERSON>", "hours_other": "Годи<PERSON>и", "minutes_one": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>", "minutes_other": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds_one": "Секунда", "seconds_other": "Секунди"}}