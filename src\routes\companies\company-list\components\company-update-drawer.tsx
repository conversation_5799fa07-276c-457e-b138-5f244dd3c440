import { Button, Drawer, Input, Label, Select, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useUpdateCompany } from "../../../../hooks/api/companies"
import { useRegions } from "../../../../hooks/api/regions"
import { Company, AdminUpdateCompany } from "../../../../types"

interface CompanyUpdateDrawerProps {
  company: Company
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyUpdateDrawer = ({ company, open, setOpen }: CompanyUpdateDrawerProps) => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState<any>({
    name: company.name || "",
    email: company.email || "",
    phone: company.phone || "",
    address: company.address || "",
    city: company.city || "",
    state: company.state || "",
    zip: company.zip || "",
    country: company.country || "",
    currency_code: company.currency_code || "",
    logo_url: company.logo_url || "",
  })

  const { regions, isPending: regionsLoading } = useRegions()

  const currencyCodes = regions?.map((region) => region.currency_code)
  const countries = regions?.flatMap((region) => region.countries)

  const { mutateAsync, isPending } = useUpdateCompany(company.id, {
    onSuccess: () => {
      toast.success(t("companies.toasts.updateSuccess", "Successfully updated company"))
      setOpen(false)
      // 刷新浏览器以确保数据更新
      window.location.reload()
    },
    onError: (error) => {
      toast.error(t("companies.toasts.updateError", "Failed to update company"))
    },
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleCurrencyChange = (value: string) => {
    setFormData({ ...formData, currency_code: value })
  }

  const handleCountryChange = (value: string) => {
    setFormData({ ...formData, country: value })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // 发送数据，使用正确的字段名
    const supportedData = {
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      address: formData.address,
      city: formData.city,
      state: formData.state,
      zip: formData.zip,
      country: formData.country,
      currency_code: formData.currency_code,
      logo_url: formData.logo_url,
    }

    console.log("Sending data:", supportedData)
    mutateAsync(supportedData)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>{t("companies.actions.edit", "Edit Company")}</Drawer.Title>
        </Drawer.Header>
        
        <form onSubmit={handleSubmit}>
          <Drawer.Body className="p-4 space-y-4">
            <div className="space-y-2">
              <Label size="small">{t("companies.fields.name", "Company Name")}</Label>
              <Input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t("companies.placeholders.name", "输入公司名称")}
                required
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.phone", "Phone")}</Label>
              <Input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder={t("companies.placeholders.phone", "输入电话号码")}
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.email", "Email")}</Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder={t("companies.placeholders.email", "输入邮箱地址")}
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.address", "Address")}</Label>
              <Input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder={t("companies.placeholders.address", "输入街道地址")}
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.city", "City")}</Label>
              <Input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleChange}
                placeholder={t("companies.placeholders.city", "输入城市")}
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.state", "State")}</Label>
              <Input
                type="text"
                name="state"
                value={formData.state}
                onChange={handleChange}
                placeholder={t("companies.placeholders.state", "输入省/州")}
              />
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.zipCode", "Zip Code")}</Label>
              <Input
                type="text"
                name="zip"
                value={formData.zip}
                onChange={handleChange}
                placeholder={t("companies.placeholders.zip", "输入邮编")}
              />
            </div>

            <div className="flex gap-4 w-full">
              <div className="flex flex-col gap-2 w-1/2">
                <Label size="small">{t("companies.fields.country", "Country")}</Label>
                <Select
                  name="country"
                  value={formData.country || ""}
                  onValueChange={handleCountryChange}
                  disabled={regionsLoading}
                >
                  <Select.Trigger disabled={regionsLoading}>
                    <Select.Value placeholder={t("companies.placeholders.selectCountry", "Select Country")} />
                  </Select.Trigger>
                  <Select.Content className="z-50">
                    {countries?.map((country) => (
                      <Select.Item
                        key={country?.iso_2 || ""}
                        value={country?.iso_2 || ""}
                      >
                        {country?.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>
              <div className="flex flex-col gap-2 w-1/2">
                <Label size="small">{t("companies.fields.currency", "Currency")}</Label>
                <Select
                  name="currency_code"
                  value={formData.currency_code || ""}
                  onValueChange={handleCurrencyChange}
                  disabled={regionsLoading}
                >
                  <Select.Trigger disabled={regionsLoading}>
                    <Select.Value placeholder={t("companies.placeholders.selectCurrency", "Select Currency")} />
                  </Select.Trigger>
                  <Select.Content className="z-50">
                    {currencyCodes?.map((currencyCode) => (
                      <Select.Item key={currencyCode} value={currencyCode}>
                        {currencyCode?.toUpperCase()}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label size="small">{t("companies.fields.logoUrl", "Logo URL")}</Label>
              <Input
                type="text"
                name="logo_url"
                value={formData.logo_url}
                onChange={handleChange}
                placeholder="https://example.com/logo.png"
              />
            </div>
          </Drawer.Body>
          
          <Drawer.Footer>
            <Button type="button" variant="secondary" onClick={() => setOpen(false)}>
              {t("actions.cancel", "取消")}
            </Button>
            <Button type="submit" isLoading={isPending}>
              {t("actions.save", "保存")}
            </Button>
          </Drawer.Footer>
        </form>
      </Drawer.Content>
    </Drawer>
  )
}
