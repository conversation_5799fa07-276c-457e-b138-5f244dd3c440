import { useTranslation } from "react-i18next"
import type { Filter } from "../../../../../components/table/data-table"

export const useQuotesTableFilters = (): Filter[] => {
  const { t } = useTranslation()

  const statusFilter: Filter = {
    key: "status",
    label: t("quotes.filters.status", "Status"),
    type: "select",
    multiple: true,
    options: [
      {
        label: t("quotes.status.pending_merchant", "Pending Merchant"),
        value: "pending_merchant",
      },
      {
        label: t("quotes.status.pending_customer", "Pending Customer"),
        value: "pending_customer",
      },
      {
        label: t("quotes.status.merchant_rejected", "Merchant Rejected"),
        value: "merchant_rejected",
      },
      {
        label: t("quotes.status.customer_rejected", "Customer Rejected"),
        value: "customer_rejected",
      },
      {
        label: t("quotes.status.accepted", "Accepted"),
        value: "accepted",
      },
    ],
  }

  const dateFilters: Filter[] = [
    { label: t("fields.createdAt", "Created At"), key: "created_at" },
    { label: t("fields.updatedAt", "Updated At"), key: "updated_at" },
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date",
  }))

  return [statusFilter, ...dateFilters]
}