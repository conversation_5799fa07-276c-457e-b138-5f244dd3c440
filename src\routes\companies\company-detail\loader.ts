import { LoaderFunctionArgs } from "react-router-dom"
import { sdk } from "../../../lib/client"
import { AdminCompanyResponse } from "../../../types"

export const companyLoader = async ({ params }: LoaderFunctionArgs) => {
  const { id } = params

  if (!id) {
    throw new Response("Company ID is required", { status: 400 })
  }

  try {
    const response = await sdk.client.fetch<AdminCompanyResponse>(`/admin/companies/${id}`, {
      method: "GET",
      query: {
        fields: "*employees,*employees.customer,*customer_group,*approval_settings"
      }
    })
    
    return response
  } catch (error) {
    throw new Response("Company not found", { status: 404 })
  }
}