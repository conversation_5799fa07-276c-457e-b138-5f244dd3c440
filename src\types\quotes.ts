export interface Quote {
  id: string
  customer?: {
    id: string
    email: string
    first_name: string
    last_name: string
    company_name?: string  // 公司名称在 customer 对象中
    phone?: string
    has_account?: boolean
    metadata?: any
    created_by?: string
    created_at?: string
    updated_at?: string
    deleted_at?: string
    employee?: {
      id: string
      spending_limit?: number
      company?: {
        id: string
        name: string
        currency_code: string
      }
    }
  }
  status: 'pending_merchant' | 'pending_customer' | 'merchant_rejected' | 'customer_rejected' | 'accepted'
  created_at: string
  updated_at: string
  cart_id?: string
  draft_order_id?: string
  customer_id?: string
  order_change_id?: string
  messages?: QuoteMessage[]
  draft_order?: {
    id: string
    display_id: number
    total?: number
    currency_code?: string
    status?: string
    version?: number
    summary?: any
    subtotal?: number
    tax_total?: number
    discount_total?: number
    discount_tax_total?: number
    original_total?: number
    original_tax_total?: number
    item_total?: number
    item_subtotal?: number
    item_tax_total?: number
    original_item_total?: number
    original_item_subtotal?: number
    original_item_tax_total?: number
    shipping_total?: number
    shipping_subtotal?: number
    shipping_tax_total?: number
    original_shipping_tax_total?: number
    original_shipping_subtotal?: number
    original_shipping_total?: number
    items?: any[]
    customer?: {
      id: string
      email: string
      first_name: string
      last_name: string
      phone?: string
      employee?: {
        id: string
        spending_limit?: number
        company?: {
          id: string
          name: string
          currency_code: string
        }
      }
    }
  }
  cart?: {
    id: string
  }
  order_change?: {
    id: string
    actions?: any[]
  }
}

export interface QuoteItem {
  id: string
  title: string
  quantity: number
  unit_price: number
  total: number
  variant_id?: string
  product_id?: string
}

export interface QuoteMessage {
  id: string
  quote_id: string
  message: string
  created_at: string
  author_type: 'admin' | 'customer'
  author_id: string
}

export interface QuoteFilterParams {
  limit?: number
  offset?: number
  status?: Quote['status']
  customer_id?: string
  company_id?: string
  created_at?: {
    gte?: string
    lte?: string
  }
  order?: string
  fields?: string
}

export interface AdminQuoteResponse {
  quote: Quote
}

export interface AdminQuotesResponse {
  quotes: Quote[]
  count: number
  offset: number
  limit: number
}

export interface AdminCreateQuoteMessage {
  text: string
  item_id?: string
}