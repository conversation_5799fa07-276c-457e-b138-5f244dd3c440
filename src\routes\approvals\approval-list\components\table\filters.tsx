import { useTranslation } from "react-i18next"
import { Filter } from "../../../../../components/table/data-table/data-table-filter/data-table-filter"

export const useApprovalsTableFilters = (): Filter[] => {
  const { t } = useTranslation()

  const statusFilter: Filter = {
    key: "status",
    label: t("approvals.filters.status", "Status"),
    type: "select",
    multiple: true,
    options: [
      {
        label: t("approvals.status.pending", "Pending"),
        value: "pending",
      },
      {
        label: t("approvals.status.approved", "Approved"),
        value: "approved",
      },
      {
        label: t("approvals.status.rejected", "Rejected"),
        value: "rejected",
      },
      {
        label: t("approvals.status.expired", "Expired"),
        value: "expired",
      },
    ],
  }

  const createdAtFilter: Filter = {
    key: "created_at",
    label: t("fields.createdAt", "Created At"),
    type: "date",
  }

  const updatedAtFilter: Filter = {
    key: "updated_at", 
    label: t("fields.updatedAt", "Updated At"),
    type: "date",
  }

  return [statusFilter, createdAtFilter, updatedAtFilter]
}
