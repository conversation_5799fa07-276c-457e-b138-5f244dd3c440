import { But<PERSON>, <PERSON>er, Switch, Label, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useUpdateApprovalSettings } from "../../../../hooks/api/approvals"
import { Company } from "../../../../types"

interface CompanyApprovalSettingsDrawerProps {
  company: Company
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyApprovalSettingsDrawer = ({ company, open, setOpen }: CompanyApprovalSettingsDrawerProps) => {
  const { t } = useTranslation()
  
  const [requiresAdminApproval, setRequiresAdminApproval] = useState(
    company.approval_settings?.requires_admin_approval || false
  )
  const [requiresSalesManagerApproval, setRequiresSalesManagerApproval] = useState(
    company.approval_settings?.requires_sales_manager_approval || false
  )

  const { mutateAsync, isPending } = useUpdateApprovalSettings(company.id, {
    onSuccess: () => {
      toast.success(t("companies.approvalSettings.updateSuccess", "Successfully updated approval settings"))
      setOpen(false)
    },
    onError: (error) => {
      toast.error(t("companies.approvalSettings.updateError", "Failed to update approval settings"))
    },
  })

  const handleSubmit = async () => {
    const payload: any = {
      requires_admin_approval: requiresAdminApproval,
      requires_sales_manager_approval: requiresSalesManagerApproval,
    }

    // 只有当审批设置存在时才传递ID
    if (company.approval_settings?.id) {
      payload.id = company.approval_settings.id
    }

    await mutateAsync(payload)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>{t("companies.approvalSettings.title", "Approval Settings")}</Drawer.Title>
        </Drawer.Header>
        
        <Drawer.Body className="p-4 space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label size="small">
                  {t("companies.approvalSettings.requiresAdminApproval", "Requires Admin Approval")}
                </Label>
                <p className="text-ui-fg-muted text-sm">
                  {t("companies.approvalSettings.requiresAdminApprovalDesc", "Orders from this company require admin approval before processing")}
                </p>
              </div>
              <Switch
                checked={requiresAdminApproval}
                onCheckedChange={setRequiresAdminApproval}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label size="small">
                  {t("companies.approvalSettings.requiresSalesManagerApproval", "Requires Sales Manager Approval")}
                </Label>
                <p className="text-ui-fg-muted text-sm">
                  {t("companies.approvalSettings.requiresSalesManagerApprovalDesc", "Orders from this company require sales manager approval before processing")}
                </p>
              </div>
              <Switch
                checked={requiresSalesManagerApproval}
                onCheckedChange={setRequiresSalesManagerApproval}
              />
            </div>
          </div>
        </Drawer.Body>
        
        <Drawer.Footer>
          <Button variant="secondary" onClick={() => setOpen(false)}>
            {t("actions.cancel", "取消")}
          </Button>
          <Button onClick={handleSubmit} isLoading={isPending}>
            {t("actions.save", "保存")}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  )
}
