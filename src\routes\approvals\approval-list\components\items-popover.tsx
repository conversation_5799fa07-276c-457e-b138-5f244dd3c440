
import { Popover, Table, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { getStylizedAmount } from "../../../../lib/money-amount-helpers"

interface ItemsPopoverProps {
  items: Array<{
    id: string
    product_title: string
    variant_title?: string
    quantity: number
    unit_price: number
    thumbnail?: string
  }>
  currencyCode?: string
}

export const ItemsPopover = ({ items, currencyCode = "EUR" }: ItemsPopoverProps) => {
  const { t } = useTranslation()

  if (!items || items.length === 0) {
    return <Text>-</Text>
  }

  const formatAmount = (amount: number) => {
    return getStylizedAmount(amount, currencyCode)
  }

  return (
    <Popover>
      <Popover.Trigger className="hover:cursor-pointer text-left">
        <span className="underline underline-offset-4">
          {items.length} {t("fields.items", "item")}
          {items.length !== 1 ? "s" : ""}
        </span>
      </Popover.Trigger>
      <Popover.Content className="p-0 mr-10">
        <div className="max-h-96 overflow-y-auto">
          <Table>
            <Table.Body>
              {items.map((item) => (
                <Table.Row key={item.id} className="py-2 hover:bg-transparent">
                  <Table.Cell>
                    <div className="w-10 h-10 overflow-hidden flex items-center justify-center">
                      {item.thumbnail ? (
                        <img
                          src={item.thumbnail}
                          alt={item.product_title}
                          className="h-10 object-contain mr-4"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                          <Text size="small">?</Text>
                        </div>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell className="py-2">
                    <Text className="font-medium truncate max-w-60">
                      {item.product_title}
                    </Text>
                    {item.variant_title && (
                      <Text className="text-sm text-gray-500">
                        {item.variant_title}
                      </Text>
                    )}
                    <Text className="text-sm text-gray-500">
                      {item.quantity} x {formatAmount(item.unit_price)}
                      <br />
                      <Text className="font-medium">
                        {t("orders.summary.itemTotal", "Item total")}: {formatAmount(item.quantity * item.unit_price)}
                      </Text>
                    </Text>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      </Popover.Content>
    </Popover>
  )
}
