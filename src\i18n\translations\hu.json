{"$schema": "./$schema.json", "general": {"ascending": "Növekvő", "descending": "Csökkenő", "add": "Hozzáadás", "start": "Kezdés", "end": "Vége", "open": "Megnyitás", "close": "Bezárás", "apply": "Alkalmaz", "range": "Tartomány", "search": "Keresés", "of": "of", "results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pages": "oldalak", "next": "Következő", "prev": "Előző", "is": "is", "timeline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON>", "warning": "Figyelmeztetés", "tip": "<PERSON><PERSON><PERSON>", "error": "Hiba", "select": "<PERSON><PERSON><PERSON><PERSON>", "selected": "Kiválasztva", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktív", "revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "Eltávolítva", "admin": "Admin", "store": "Áru<PERSON><PERSON><PERSON>", "details": "Részletek", "items_one": "{{count}} elem", "items_other": "{{count}} elem", "countSelected": "{{count}} kiválasztva", "countOfTotalSelected": "{{count}}/{{total}} kiválasztva", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} to<PERSON><PERSON><PERSON><PERSON>", "areYouSure": "<PERSON><PERSON><PERSON> benne?", "areYouSureDescription": "A(z) {{entity}} {{title}} törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>.", "noRecordsFound": "<PERSON><PERSON><PERSON>", "typeToConfirm": "<PERSON><PERSON><PERSON> be a megerősítéshez: {val}", "noResultsTitle": "<PERSON><PERSON><PERSON>", "noResultsMessage": "Próbálja meg módosítani a keresési feltételeket.", "noSearchResults": "<PERSON><PERSON><PERSON>", "noSearchResultsFor": "<PERSON><PERSON><PERSON> ta<PERSON> <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON><PERSON>", "noRecordsMessage": "<PERSON><PERSON><PERSON> ta<PERSON> a keresési feltételek alapján.", "unsavedChangesTitle": "Biztos ki akar l<PERSON>?", "unsavedChangesDescription": "Az Ön által végzett változtatások nem lesznek mentve.", "includesTaxTooltip": "Az oszlopban szereplő árak adót tartalmaznak.", "excludesTaxTooltip": "<PERSON>z oszlopban szereplő árak nem tartalmaznak adót.", "noMoreData": "<PERSON><PERSON><PERSON> több adat"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} kulcs", "numberOfKeys_other": "{{count}} kulcs", "drawer": {"header_one": "JSON <0>· {{count}} kulcs</0>", "header_other": "JSON <0>· {{count}} kulcs</0>", "description": "Nézze meg a JSON objektumot"}}, "metadata": {"header": "Metaadatok", "numberOfKeys_one": "{{count}} kulcs", "numberOfKeys_other": "{{count}} kulcs", "edit": {"header": "Metaadatok szerkesztése", "description": "Szerkessze a metaadatokat.", "successToast": "Metaadatok sikeresen frissítve.", "actions": {"insertRowAbove": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "insertRowBelow": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "deleteRow": "<PERSON><PERSON> t<PERSON>"}, "labels": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "complexRow": {"label": "Néhány sor le <PERSON> tilt<PERSON>.", "description": "Ez az objektum nem elsődleges metaadatokat tartalmaz, például tömböket vagy objektumokat, amelyek itt nem szerkeszthetők. A letiltott sorok szerkesztéséhez használja közvetlenül az API-t.", "tooltip": "Ez a sor le van tilt<PERSON>, mert nem elsődleges adatokat tartalmaz."}}}, "validation": {"mustBeInt": "Az értéknek egész számnak kell lennie.", "mustBePositive": "Az értéknek pozitívnak kell lennie."}, "actions": {"save": "Men<PERSON>s", "saveAsDraft": "Mentés <PERSON>z<PERSON>", "copy": "Másolás", "copied": "M<PERSON>ol<PERSON>", "duplicate": "Duplikálás", "publish": "Közzététel", "create": "Létrehozás", "delete": "Törlés", "remove": "Eltávolítás", "revoke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "forceConfirm": "Megerősí<PERSON><PERSON>", "continueEdit": "Szerkesztés folytatása", "enable": "Engedélyezés", "disable": "<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "complete": "Befejezés", "viewDetails": "Részletek megtekintése", "back": "<PERSON><PERSON><PERSON>", "close": "Bezárás", "showMore": "Továbbiak megjelenítése", "continue": "Folytatás", "continueWithEmail": "Folytatás e-maillel", "idCopiedToClipboard": "Az azonosító másolva a vágólapra", "addReason": "Indok hozzáadása", "addNote": "Megjegyzés hozzáadása", "reset": "Visszaállítás", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Szerkesztés", "addItems": "<PERSON><PERSON><PERSON>", "download": "Letöltés", "clear": "Törlés", "clearAll": "Összes törlése", "apply": "Alkalmaz", "add": "Hozzáadás", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "Böngészés", "logout": "Kijelentkezés", "hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "export": "Export", "import": "Import", "cannotUndo": "<PERSON><PERSON>"}, "operators": {"in": "In"}, "app": {"search": {"label": "Keresés", "title": "Keresés", "description": "Keresés a teljes adminisztrá<PERSON>ós felületen", "allAreas": "<PERSON><PERSON> terü<PERSON>", "navigation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openResult": "<PERSON><PERSON><PERSON><PERSON>", "showMore": "Továbbiak megjelenítése", "placeholder": "Keresés", "noResultsTitle": "<PERSON><PERSON><PERSON>", "noResultsMessage": "Próbálja meg módosítani a keresési feltételeket.", "emptySearchTitle": "Kezdjen el keresni", "emptySearchMessage": "Kezdjen el keresni a teljes adminisztrációs felületen.", "loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} ta<PERSON><PERSON><PERSON> betöltése", "groups": {"all": "Minden", "customer": "Vásárlók", "customerGroup": "Vásárlói csoportok", "product": "Termékek", "productVariant": "Termékvariánsok", "inventory": "<PERSON><PERSON><PERSON><PERSON>", "reservation": "Foglalások", "category": "Kate<PERSON><PERSON><PERSON><PERSON>", "collection": "Gyűjtemények", "order": "<PERSON><PERSON><PERSON><PERSON>", "promotion": "Promóciók", "campaign": "Kampányok", "priceList": "<PERSON><PERSON><PERSON><PERSON>", "user": "Felhasználók", "region": "Régiók", "taxRegion": "Adórégiók", "returnReason": "Visszaküldési okok", "salesChannel": "Értékesítési <PERSON>", "productType": "Terméktípusok", "productTag": "Termékcímkék", "location": "<PERSON><PERSON><PERSON>", "shippingProfile": "Szállítási profilok", "publishableApiKey": "Publikus API kulcsok", "secretApiKey": "Titkos API kulcsok", "command": "Parancsok", "navigation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "keyboardShortcuts": {"pageShortcut": "Ugrás", "settingShortcut": "Beállítások", "commandShortcut": "Parancsok", "then": "then", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON><PERSON>", "goToProducts": "Termékek", "goToCollections": "Gyűjtemények", "goToCategories": "Kate<PERSON><PERSON><PERSON><PERSON>", "goToCustomers": "Vásárlók", "goToCustomerGroups": "Vásárlói csoportok", "goToInventory": "<PERSON><PERSON><PERSON><PERSON>", "goToReservations": "Foglalások", "goToPriceLists": "<PERSON><PERSON><PERSON><PERSON>", "goToPromotions": "Promóciók", "goToCampaigns": "Kampányok"}, "settings": {"goToSettings": "Beállítások", "goToStore": "Áru<PERSON><PERSON><PERSON>", "goToUsers": "Felhasználók", "goToRegions": "Régiók", "goToTaxRegions": "Adórégiók", "goToSalesChannels": "Értékesítési <PERSON>", "goToProductTypes": "Terméktípusok", "goToLocations": "<PERSON><PERSON><PERSON>", "goToPublishableApiKeys": "Publikus API kulcsok", "goToSecretApiKeys": "Titkos API kulcsok", "goToWorkflows": "Munkafolyamatok", "goToProfile": "Profil", "goToReturnReasons": "Visszaküldési okok"}}, "menus": {"user": {"documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "changelog": "Változásnapló", "shortcuts": "Billentyűpar<PERSON>ok", "profileSettings": "Profilbeállítások", "theme": {"label": "<PERSON><PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON>il<PERSON><PERSON>", "system": "Rendszer"}}, "store": {"label": "Áru<PERSON><PERSON><PERSON>", "storeSettings": "Áruházbeállítások"}, "actions": {"logout": "Kijelentkezés"}}, "nav": {"accessibility": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A felület navigációs menüje"}, "common": {"extensions": "Bővítmények"}, "main": {"store": "Áru<PERSON><PERSON><PERSON>", "storeSettings": "Áruházbeállítások"}, "settings": {"header": "Beállítások", "general": "<PERSON><PERSON>lán<PERSON>", "developer": "Fejlesztő", "myAccount": "<PERSON><PERSON><PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetToDefault": "Alaphelyzetbe állítás", "disabled": "Az oszlopok láthatóságának változtatása le van tiltva"}, "shortcuts": {"label": "Billentyűpar<PERSON>ok", "commands": {"undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "Is<PERSON><PERSON><PERSON>", "copy": "Másolás", "paste": "Beillesztés", "edit": "Szerkesztés", "delete": "Törlés", "clear": "Törlés", "moveUp": "Ugrás fel", "moveDown": "Ugr<PERSON> le", "moveLeft": "<PERSON><PERSON><PERSON><PERSON> balra", "moveRight": "<PERSON><PERSON><PERSON><PERSON>", "moveTop": "Ugrás a tetejére", "moveBottom": "Ugrás az aljára", "selectDown": "Ki<PERSON><PERSON><PERSON><PERSON><PERSON> le", "selectUp": "Kijelölés fel", "selectColumnDown": "Oszlop ki<PERSON>lölé<PERSON> le", "selectColumnUp": "Oszlop ki<PERSON>lölése fel", "focusToolbar": "Ugrás a felső eszköztárra", "focusCancel": "Ugrás a Mégse gombra"}}, "errors": {"fixError": "Hiba javítás<PERSON>", "count_one": "{{count}} hiba", "count_other": "{{count}} hiba"}}, "filters": {"sortLabel": "Rendez<PERSON>", "filterLabel": "Szűrés", "searchLabel": "Keresés", "date": {"today": "Ma", "lastSevenDays": "Elmúlt 7 nap", "lastThirtyDays": "Elmúlt 30 nap", "lastNinetyDays": "Elmúlt 90 nap", "lastTwelveMonths": "Elmúlt 12 hónap", "custom": "<PERSON><PERSON><PERSON><PERSON>", "from": "Kezdés", "to": "Vége", "starting": "Kezdés", "ending": "Vége"}, "compare": {"lessThan": "Kisebb, mint", "greaterThan": "Nagyobb, mint", "exact": "Pontosan", "range": "Tartomány", "lessThanLabel": "kisebb mint {{value}}", "greaterThanLabel": "nagyobb mint {{value}}", "andLabel": "és"}, "sorting": {"alphabeticallyAsc": "A-Z", "alphabeticallyDesc": "Z-A", "dateAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateDesc": "Leg<PERSON><PERSON><PERSON><PERSON>"}, "radio": {"yes": "Igen", "no": "Nem", "true": "Igaz", "false": "<PERSON><PERSON>"}, "addFilter": "Szűrő hozzáadása"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON>", "badRequestMessage": "A szerver nem tudta megérteni a kérést, mert a szintaxis hibás volt.", "notFoundTitle": "404 - <PERSON><PERSON> nem ta<PERSON>", "notFoundMessage": "Ellenőrizze az URL-címet, és próbálja meg új<PERSON>, vagy hasz<PERSON> a keresősávot, hogy me<PERSON>, amit keres.", "internalServerErrorTitle": "500 - Belső szerverhiba", "internalServerErrorMessage": "Váratlan hiba történt a kiszolgálón. Kérjük, próbálja meg később újra.", "defaultTitle": "<PERSON><PERSON>", "defaultMessage": "Valami hiba történ<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>, pr<PERSON>b<PERSON><PERSON><PERSON> meg k<PERSON>bb új<PERSON>.", "noMatchMessage": "<PERSON><PERSON> a keresett oldal.", "backToDashboard": "Vissza a vezérlőpulthoz"}, "addresses": {"title": "Címek", "shippingAddress": {"header": "Szállítási cím", "editHeader": "Szállítási cím szerkesztése", "editLabel": "Szállítási cím", "label": "Szállítási cím"}, "billingAddress": {"header": "Számlázási cím", "editHeader": "Számlázási cím szerkesztése", "editLabel": "Számlázási cím", "label": "Számlázási cím", "sameAsShipping": "Ugyanaz, mint a szállítási cím"}, "contactHeading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locationHeading": "<PERSON><PERSON>"}, "email": {"editHeader": "<PERSON><PERSON>", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"order": "<PERSON><PERSON><PERSON>", "draft": "Piszkozat részletei"}, "currentOwner": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "A rendelés jelenlegi tulajdonosa."}, "newOwner": {"label": "0", "hint": "A rendelés új t<PERSON>."}, "validation": {"mustBeDifferent": "Az új tulajdonosnak különböznie kell a jelenlegi tulajdonostól.", "required": "Az új tulajdonos mező kitöltése kötelező."}}, "sales_channels": {"availableIn": "<0>{{x}}</0>/<1>{{y}}</1> értékesítési csatornán elérhető"}, "products": {"domain": "Termékek", "list": {"noRecordsMessage": "Hozza létre az első terméket"}, "edit": {"header": "Termék szerkesztése", "description": "A termék részleteinek szerkesztése", "successToast": "A {{title}} term<PERSON><PERSON> si<PERSON>esen frissítve."}, "create": {"title": "<PERSON><PERSON>", "description": "Hozzon létre egy új <PERSON>", "header": "<PERSON><PERSON>lán<PERSON>", "tabs": {"details": "Részletek", "organize": "Rendszerezés", "variants": "Variánsok", "inventory": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"variants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> legalább egy variánst.", "options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ad<PERSON> hozz<PERSON> legalább egy termékopci<PERSON>t.", "uniqueSku": "A SKU értéknek egyedinek kell lennie."}, "inventory": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "label": "Elemek hozzáadása a készlethez", "itemPlaceholder": "Készletelem kiválasztása", "quantityPlaceholder": "Mennyi szükséges a készlethez?"}, "variants": {"header": "Variánsok", "subHeadingTitle": "<PERSON><PERSON>, ennek a terméknek vannak variánsai", "subHeadingDescription": "Ha nincs be<PERSON>, akkor létrehozunk egy alapértelmezett változatot az Ön számára", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "Kicsi, Közepes, Nagy"}, "productVariants": {"label": "Termékvariánsok", "hint": "Ez a rangsorolás befolyásolja a változatok sorrendjét az áruházban.", "alert": "Adjon hozzá opciókat a variánsok létrehozásához", "tip": "A kijelöletlen változatok nem jönnek létre. A variánsokat utólag is létrehozhatja és szerkesztheti, de ez a lista illeszkedik a termékopciókban szereplő variációkhoz."}, "productOptions": {"label": "Termékopciók", "hint": "Az opciók segítségével meghatározhatja a termék tulajdon<PERSON>ágait, péld<PERSON><PERSON> a színt vagy a méretet."}}, "successToast": "A {{title}} term<PERSON><PERSON>."}, "export": {"header": "Terméklista exportálása", "description": "Exportálja a termékeket egy CSV fájlba", "success": {"title": "Feldolgozás folyamatban", "description": "Az exportálás néhány percet igénybe vehet. Értesítjük, amikor kész."}, "filters": {"title": "Szűrők", "description": "Csökkentse a listát a kívánt termékek kiválasztásával"}, "columns": {"title": "Oszlopok", "description": "Válassza ki azokat az oszlopokat, amelyeket a CSV fájl tartalmazni fog"}}, "import": {"header": "Terméklista importálása", "uploadLabel": "Terméklista importálása", "uploadHint": "Húzza ide a CSV fájlt, vag<PERSON> katti<PERSON> a feltöltéshez", "description": "Importáljon termékeket egy CSV fájlból", "template": {"title": "Nem biztos benne milyen formátumot használjon?", "description": "<PERSON><PERSON><PERSON><PERSON> le az al<PERSON><PERSON>, hogy megbizonyosodjon arr<PERSON>, hogy a helyes formá<PERSON>ot követ<PERSON>."}, "upload": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Az importálással termékeket adhat hozzá vagy frissíthet. A meglévő termékek frissítéséhez a meglévő fogantyút és azonosítót, a meglévő változatok frissítéséhez a meglévő azonosítót kell használnia. A termékek importálása előtt megerősítést kérünk Öntől.", "preprocessing": "Előfeldolgozás...", "productsToCreate": "Termékek lesznek létrehozva", "productsToUpdate": "Termékek lesznek frissítve"}, "success": {"title": "Feldolgozás folyamatban", "description": "Az importálás néhány percet igénybe vehet. Értesítjük, amikor kész."}}, "deleteWarning": "A {{title}} termék törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "variants": {"header": "Variánsok", "empty": {"heading": "<PERSON><PERSON><PERSON><PERSON> varián<PERSON>k", "description": "Nincsenek megjeleníthető variánsok"}, "filtered": {"heading": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ta<PERSON> a keresési feltételek alapján."}}, "attributes": "Attribútumok", "editAttributes": "Attribútumok szerkesztése", "editOptions": "Opciók szerkesztése", "editPrices": "<PERSON><PERSON>", "media": {"label": "Média", "editHint": "A termék média szerkesztése", "makeThumbnail": "Előnézeti kép választása", "uploadImagesLabel": "Képek feltöltése", "uploadImagesHint": "<PERSON><PERSON><PERSON> ide a k<PERSON><PERSON><PERSON>, vagy katti<PERSON> a feltöltéshez", "invalidFileType": "'{{name}}' nem tá<PERSON>gatott formátum. Támogatott formátumok: {{types}}.", "failedToUpload": "A fájl feltöltése sikertelen. Próbálja újra.", "deleteWarning_one": "{{count}} ké<PERSON> k<PERSON> törölni. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "deleteWarning_other": "{{count}} ké<PERSON> k<PERSON> törölni. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "deleteWarningWithThumbnail_one": "{{count}} képet k<PERSON>ü<PERSON>ö<PERSON>, köztük az előnézeti képet is. Ez a művelet nem von<PERSON>ó vissza.", "deleteWarningWithThumbnail_other": "{{count}} képet k<PERSON>ü<PERSON>ö<PERSON>, köztük az előnézeti képet is. Ez a művelet nem von<PERSON>ó vissza.", "thumbnailTooltip": "Előnézeti kép", "galleryLabel": "Galéria", "downloadImageLabel": "<PERSON><PERSON><PERSON>", "deleteImageLabel": "<PERSON><PERSON><PERSON>", "emptyState": {"header": "<PERSON><PERSON><PERSON><PERSON> képek", "description": "Nincsenek megjeleníthető képek", "action": "Képek feltöltése"}, "successToast": "A média sikeresen frissítve."}, "discountableHint": "<PERSON> k<PERSON>csol<PERSON>, a termékre nem alkalmazhatók kedvezmények.", "noSalesChannels": "A termék nem érhető el egyetlen értékesítési csatornán sem.", "variantCount_one": "{{count}} variáns", "variantCount_other": "{{count}} variáns", "deleteVariantWarning": "A {{title}} variáns törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>.", "productStatus": {"draft": "Piszkozat", "published": "Közzétéve", "proposed": "Javasolt", "rejected": "Elutasítva"}, "fields": {"title": {"label": "Cím", "hint": "Adjon rövid és egyértelmű címet a termékének.<0/>50-60 karakter az ajánlott hossz a keresőmotorok számára."}, "subtitle": {"label": "Alcím"}, "handle": {"label": "Fogantyú", "tooltip": "A fogantyú a termékre való hivatkozásra szolgál a kirakatban. Ha nincs megadva, a fogantyú a termék címéből generálódik."}, "description": {"label": "Le<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON> rövid <PERSON> vilá<PERSON> le<PERSON> a termékhez.<0/>120-160 karakter az ajánlott ho<PERSON>zúság a keresőmotorok számára."}, "discountable": {"label": "Kedvezményezhető", "hint": "<PERSON> k<PERSON>csol<PERSON>, a termékre nem alkalmazhatók kedvezmények."}, "shipping_profile": {"label": "Szállítási profil", "hint": "Termék csatolása egy szállítási profilhoz."}, "type": {"label": "<PERSON><PERSON><PERSON>"}, "collection": {"label": "Gyűjtemény"}, "categories": {"label": "Kate<PERSON><PERSON><PERSON><PERSON>"}, "tags": {"label": "Címkék"}, "sales_channels": {"label": "Értékesítési <PERSON>", "hint": "Ez a termék csak az alapértelmezett értékesítési csatornán lesz elérhető, ha érintetlenül hagyja."}, "countryOrigin": {"label": "Származási ország"}, "material": {"label": "<PERSON><PERSON>"}, "width": {"label": "Szélesség"}, "length": {"label": "Hossz"}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "weight": {"label": "Súly"}, "options": {"label": "Termékopciók", "hint": "A termék tulajdonságainak meghatározásához adja meg a termékopciókat, péld<PERSON><PERSON> a színt vagy a méretet.", "add": "<PERSON><PERSON><PERSON>", "optionTitle": "<PERSON><PERSON><PERSON> c<PERSON>", "optionTitlePlaceholder": "Szín", "variations": "Változatok (vesszővel elválasztva)", "variantionsPlaceholder": "Piros, Kék, Zöld"}, "variants": {"label": "Variánsok", "hint": "A kijelöletlen variánsok nem jönnek létre. Ez a rangsorolás befolyásolja a variánsok rangsorolását a webáruházban."}, "mid_code": {"label": "MID kód"}, "hs_code": {"label": "HS kód"}}, "variant": {"edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "success": "Vari<PERSON>s si<PERSON>n fris<PERSON>ít<PERSON>."}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteWarning": "Biztosan törölni szeretné a variánst?", "pricesPagination": "1 - {{current}} - {{total}} árak", "tableItemAvailable": "{{availableCount}} elé<PERSON><PERSON><PERSON><PERSON>", "tableItem_one": "{{availableCount}} el<PERSON>r<PERSON><PERSON><PERSON> {{locationCount}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "tableItem_other": "{{availableCount}} el<PERSON>r<PERSON><PERSON><PERSON> {{locationCount}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": {"notManaged": "<PERSON><PERSON>", "manageItems": "Készletelemek kezelése", "notManagedDesc": "Ennél a változatnál a készlet nem kerül kezelésre. Kapcsolja be a „Készlet kezelése” opciót a változat készletének nyomon követéséhez.", "manageKit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g kez<PERSON>", "navigateToItem": "Ugrás a készletelemhez", "actions": {"inventoryItems": "Ugrás a készletelemhez", "inventoryKit": "Készletelemek mutatása"}, "inventoryKit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventoryKitHint": "Ez a variáns egy k<PERSON>, amely több készletelemet tartalmaz?", "validation": {"itemId": "Készletelem kiválasztása kötelező.", "quantity": "A mennyiség mező kitöltése kötelező."}, "header": "K<PERSON>zletkezelés", "editItemDetails": "Készletelem részleteinek szerkesztése", "manageInventoryLabel": "K<PERSON>zlet automatikus kezelése", "manageInventoryHint": "Ha engedélyez<PERSON> van, ak<PERSON> a megrendelések és visszáru létrehozásakor megváltoztatjuk a készlet mennyiségét.", "allowBackordersLabel": "Visszarendelések engedélyezése", "allowBackordersHint": "Ha engedélyez<PERSON> van, a vásárlók akkor is megvásárolhatják a változatot, ha nem áll rendelkezésre elegendő mennyiség.", "toast": {"levelsBatch": "Készletszintek frissítve.", "update": "Készletelem frissítve.", "updateLevel": "Készletszint frissítve.", "itemsManageSuccess": "Készletelemek sikeresen frissítve."}}}, "options": {"header": "Opciók", "edit": {"header": "<PERSON><PERSON><PERSON>keszté<PERSON>", "successToast": "{{title}} opci<PERSON> si<PERSON>esen frissítve."}, "create": {"header": "<PERSON>ció létrehoz<PERSON>", "successToast": "{{title}} op<PERSON><PERSON>."}, "deleteWarning": "A(z) {{title}} opció törlésére készül. Ez a művelet nem vonható vissza."}, "organization": {"header": "Szervezet", "edit": {"header": "Szervezet szerkesztése", "toasts": {"success": "{{title}} szervezete sikeresen frissítve."}}}, "stock": {"heading": "A termékkészletszintek és helyszínek kezelése", "description": "A termék összes változatának készletszintjének frissítése.", "loading": "<PERSON><PERSON> pillanat...", "tooltips": {"alreadyManaged": "Ez a leltári tétel már s<PERSON>keszthető {{title}} név alatt.", "alreadyManagedWithSku": "Ez a leltári tétel már s<PERSON>keszthető {{title}} ({{sku}}) név alatt."}}, "shippingProfile": {"header": "Szállítási profil", "edit": {"header": "Szállítási konfiguráció", "toasts": {"success": "Szállítási profil sikeresen szerkesztve a(z) {{title}} termékhez."}}, "create": {"errors": {"required": "A szállítási profil mező kitöltése kötelező."}}}, "toasts": {"delete": {"success": {"header": "Termék törölve", "description": "{{title}} si<PERSON><PERSON><PERSON> tö<PERSON>."}, "error": {"header": "Hiba történt a termék törlésekor"}}}}, "collections": {"domain": "Gyűjtemények", "subtitle": "Rendezze gyűjteményekbe a termékeket.", "createCollection": "Gyűjtemény létrehozása", "createCollectionHint": "Hozzon létre egy új g<PERSON>űjteményt a termékek rendezéséhez.", "createSuccess": "Gyűjtemény si<PERSON> lé<PERSON>hozva.", "editCollection": "Gyűjtemény szerkesztése", "handleTooltip": "A fogantyú a gyűjteményre való hivatkozásra szolgál a webáruházban. Ha nincs megadva, a fogantyú a gyűjtemény címéből generálódik.", "deleteWarning": "A(z) {{title}} gyűjtemény törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "removeSingleProductWarning": "A(z) {{title}} termék gyűjteményből való eltávolítására készül. Ez a művelet nem vonható v<PERSON>za.", "removeProductsWarning_one": "{{count}} termék gyűjteményből való eltávolítására készül. Ez a művelet nem vonható vissza.", "removeProductsWarning_other": "{{count}} termék gyűjteményből való eltávolítására készül. Ez a művelet nem vonható vissza.", "products": {"list": {"noRecordsMessage": "Nincsenek termékek a gyűjteményben."}, "add": {"successToast_one": "A termék sikeresen hozzáadva a gyűjteményhez.", "successToast_other": "A termékek sikeresen hozzáadva a gyűjteményhez."}, "remove": {"successToast_one": "A termék sikeresen eltávolítva a gyűjteményből.", "successToast_other": "A termékek sikeresen eltávolítva a gyűjteményből."}}}, "categories": {"domain": "Kate<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Rendezze kategóriákba a termékeket és sorolja be a kategóriákat.", "create": {"header": "Kategória létrehozása", "hint": "Hozzon létre egy új kategóriát a termékek rendezéséhez.", "tabs": {"details": "Részletek", "organize": "Rangsorol<PERSON>"}, "successToast": "{{name}} ka<PERSON><PERSON><PERSON><PERSON>."}, "edit": {"header": "Kategória szerkesztése", "description": "A kategória részleteinek szerkesztése", "successToast": "Kategória si<PERSON>n frissítve."}, "delete": {"confirmation": "A(z) {{name}} kategória törlésére készül. Ez a művelet nem vonható v<PERSON>.", "successToast": "{{name}} kate<PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>."}, "products": {"add": {"disabledTooltip": "A termék már hozz<PERSON> van rendelve ehhez a kategóriához.", "successToast_one": "{{count}} term<PERSON><PERSON> a kategóriához.", "successToast_other": "{{count}} term<PERSON><PERSON> a kategóriához."}, "remove": {"confirmation_one": "{{count}} terméket készül eltávolítani a kategóriából. Ez a művelet nem vonható v<PERSON>.", "confirmation_other": "{{count}} terméket készül eltávolítani a kategóriából. Ez a művelet nem vonható v<PERSON>.", "successToast_one": "{{count}} term<PERSON><PERSON> si<PERSON>n eltávolítva a kategóriából.", "successToast_other": "{{count}} term<PERSON><PERSON> si<PERSON>n eltávolítva a kategóriából."}, "list": {"noRecordsMessage": "<PERSON>ncsenek termékek a kategóriában."}}, "organize": {"header": "Kategóri<PERSON>", "action": "Rangsorol<PERSON>"}, "fields": {"visibility": {"label": "<PERSON><PERSON><PERSON><PERSON>", "internal": "Belső", "public": "N<PERSON>lván<PERSON>"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktív", "inactive": "Inaktív"}, "path": {"label": "Útvonal", "tooltip": "A kategória teljes elérési útvonalának megjelenítése."}, "children": {"label": "Alkategóriák"}, "new": {"label": "<PERSON><PERSON>"}}}, "inventory": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Készletelemek kezelése", "reserved": "<PERSON><PERSON><PERSON><PERSON>", "available": "Elérhető", "locationLevels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "associatedVariants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manageLocations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manageLocationQuantity": "<PERSON><PERSON>sz<PERSON>i mennyiség kezelése", "deleteWarning": "Egy készletelem törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "editItemDetails": "Készletelem részleteinek szerkesztése", "quantityAcrossLocations": "{{quantity}} a {{locations}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"title": "<PERSON><PERSON>", "details": "Részletek", "availability": "Elérhetőség", "locations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attributes": "Attribútumok", "requiresShipping": "Szállítás szükséges", "requiresShippingHint": "Szükséges-e a termék szállítása?", "successToast": "Készletelem sikeresen létrehozva."}, "reservation": {"header": "{{itemName}} lefoglalása", "editItemDetails": "Foglalás szerkesztése", "lineItemId": "Tétel azonosító", "orderID": "Rendel<PERSON>", "description": "Le<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "inStockAtLocation": "<PERSON><PERSON><PERSON><PERSON> van a helyszínen", "availableAtLocation": "Elérhető a helyszínen", "reservedAtLocation": "Foglalt a helyszínen", "reservedAmount": "Foglalt mennyiség", "create": "Foglalás létrehozása", "itemToReserve": "Foglalni kívánt elem", "quantityPlaceholder": "Mennyit szeretne foglalni belőle?", "descriptionPlaceholder": "<PERSON><PERSON><PERSON> t<PERSON>?", "successToast": "Foglalás si<PERSON>esen létrehozva.", "updateSuccessToast": "Foglalás sikeresen frissítve.", "deleteSuccessToast": "Foglalás si<PERSON>esen törölve.", "errors": {"noAvaliableQuantity": "<PERSON><PERSON><PERSON> el<PERSON> k<PERSON>t a helyszínen.", "quantityOutOfRange": "A minimum foglalható mennyiség 1, a maximum {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "A raktározott mennyiség nem frissíthető a lefoglalt {{quantity}} darabos mennyiségnél kisebbre."}}, "toast": {"updateLocations": "<PERSON><PERSON><PERSON><PERSON><PERSON> frissítve.", "updateLevel": "Készletszint frissítve.", "updateItem": "Készletelem frissítve."}, "stock": {"title": "K<PERSON>zletkezelés", "description": "A kiválasztott leltári tételek készletszintjének frissítése.", "action": "Készletszintek szerkesztése", "placeholder": "<PERSON><PERSON>", "disablePrompt_one": "{{count}} he<PERSON><PERSON><PERSON> letiltás<PERSON>ra k<PERSON>zül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "disablePrompt_other": "{{count}} he<PERSON><PERSON><PERSON> letiltás<PERSON>ra k<PERSON>zül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "disabledToggleTooltip": "Nem lehet letiltani: a letiltás előtt törölje a bejövő és/vagy a lefoglalt mennyiséget.", "successToast": "Készletszintek sikeresen frissítve."}}, "giftCards": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editGiftCard": "Ajándékkártya szerkesztése", "createGiftCard": "Ajándékkártya létrehozása", "createGiftCardHint": "<PERSON><PERSON><PERSON> létre egy a<PERSON><PERSON><PERSON><PERSON>, amely fi<PERSON><PERSON> mód<PERSON><PERSON><PERSON> hasz<PERSON>lható az áru<PERSON>á<PERSON>ban.", "selectRegionFirst": "Először válassza ki a régiót", "deleteGiftCardWarning": "A(z) {{code}} ajándékkártya törlésére készül. Ez a művelet nem vonható v<PERSON>.", "balanceHigherThanValue": "A kártya egyenlege nem lehet magasabb, mint az érték.", "balanceLowerThanZero": "Az egyenleg nem lehet negatív.", "expiryDateHint": "Az országok eltérő törvényekkel rendelkeznek az ajándékkártyák lejárati idejét illetően. A lejárati dátum beállítása előtt mindenképpen ellenőrizze a helyi előírásokat.", "regionHint": "Az ajándékkártya régiójának megváltoztatásával annak pénzneme is megváltozik, ami befolyásolhatja annak pénzbeli értékét.", "enabledHint": "Engedélyezze vagy tiltsa le az ajándékkártyát", "balance": "<PERSON>gy<PERSON><PERSON>", "currentBalance": "Je<PERSON><PERSON><PERSON> egy<PERSON>leg", "initialBalance": "Kezdeti egyenleg", "personalMessage": "<PERSON>ze<PERSON><PERSON><PERSON>", "recipient": "Címzett"}, "customers": {"domain": "Vásárlók", "list": {"noRecordsMessage": "Itt fognak megjelenni a vásárlók."}, "create": {"header": "Vásárló létrehoz<PERSON>", "hint": "Hozzon létre egy új vásárlót.", "successToast": "Vásárló {{email}} si<PERSON><PERSON>n l<PERSON>."}, "groups": {"label": "Vásárlói csoportok", "remove": "Biztosan el akarja távolítani a vásárlót a(z) \"{{name}}\" csoportból?", "removeMany": "Biztosan el akarja távolítani a vásárlót az alábbi csoportokból: {{groups}}?", "alreadyAddedTooltip": "A vásárló már hozzá van adva a csoportokhoz.", "list": {"noRecordsMessage": "Ez a vásárló még nem tartozik egyik csoporthoz sem"}, "add": {"success": "<PERSON>ás<PERSON><PERSON><PERSON>: {{groups}}.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>n létre egy vásárl<PERSON>i csoportot."}}, "removed": {"success": "Vásá<PERSON>ó eltávolítva innen: {{groups}}.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>n létre egy vásárl<PERSON>i csoportot."}}}, "edit": {"header": "Vásárló <PERSON>té<PERSON>", "emailDisabledTooltip": "Az e-mail cím nem szerkeszthető, mert a vásárló fiókot hozott létre.", "successToast": "Vásárló {{email}} sikeresen frissítve."}, "delete": {"title": "Vásárló törl<PERSON>", "description": "Törölni készül a következő vásárlót: {{email}}. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "successToast": "{{email}} v<PERSON><PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>ö<PERSON>."}, "fields": {"guest": "Vendég", "registered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groups": "Csoportok"}, "registered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guest": "Vendég", "hasAccount": "Fiókkal rendelkezik", "addresses": {"title": "Címek", "fields": {"addressName": "<PERSON><PERSON><PERSON> neve", "address1": "Cím 1", "address2": "Cím 2", "city": "<PERSON><PERSON><PERSON>", "province": "<PERSON><PERSON>", "postalCode": "Irányí<PERSON>", "country": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telefon", "company": "Cég", "countryCode": "Országkó<PERSON>", "provinceCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "create": {"header": "Cím <PERSON>", "hint": "Hozzon létre egy új címet a vásárlónak.", "successToast": "A cím si<PERSON>esen létrehozva."}}}, "customerGroups": {"domain": "Vásárlói csoportok", "subtitle": "Szervezze a vásárlókat csoportokba. A csoportok különböző akciókkal és árakkal rendelkezhetnek.", "list": {"empty": {"heading": "Nincsenek vásárlói csoportok", "description": "Nincsenek megjeleníthető vásárlói csoportok."}, "filtered": {"heading": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ta<PERSON> a keresési feltételek alapján."}}, "create": {"header": "Vásárlói csoport létrehozása", "hint": "Hozzon létre egy új vásárlói csoportot.", "successToast": "Vásárlói csoport {{name}} létrehozva."}, "edit": {"header": "Vásárlói csoport szerkesztése", "successToast": "Vásárlói csoport {{name}} frissítve."}, "delete": {"title": "Vásárlói csoport törlése", "description": "A(z) {{name}} vásárlói csoport törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "successToast": "{{name}} vásárlói csoport sikeresen törölve."}, "customers": {"alreadyAddedTooltip": "A vásárló már hozz<PERSON> van adva a csoporthoz.", "add": {"successToast_one": "Vásárló si<PERSON>esen hozzáadva a csoporthoz.", "successToast_other": "Vásárlók sikeresen hozzáadva a csoporthoz.", "list": {"noRecordsMessage": "Hozzon létre el<PERSON> egy vásárlót"}}, "remove": {"title_one": "Vásárló eltávolítása", "title_other": "Vásárlók eltávolítása", "description_one": "{{count}} vásárlót készül eltávolítani a csoportból. Ez a művelet nem vonható v<PERSON>za.", "description_other": "{{count}} vásárlót készül eltávolítani a csoportból. Ez a művelet nem vonható v<PERSON>za."}, "list": {"noRecordsMessage": "Nincsenek vásárlók a csoportban."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "claim": "Igénylés", "exchange": "Csere", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelWarning": "A(z) {{id}} számú rendelés törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>.", "orderCanceled": "A rendelés törölve", "onDateFromSalesChannel": "{{date}} {{salesChannel}}", "list": {"noRecordsMessage": "Itt fognak megjelenni a rendelések"}, "status": {"not_paid": "<PERSON><PERSON>", "pending": "Függőben", "completed": "Befejezett", "draft": "Piszkozat", "archived": "Archivált", "canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requires_action": "Intézkedést igényel"}, "summary": {"requestReturn": "Visszaküldés igénylése", "allocateItems": "Tételek kiosztása", "editOrder": "Rendelés szerkesztése", "editOrderContinue": "Folytatás", "inventoryKit": "{{count}} k<PERSON>zletelemből áll", "itemTotal": "Tétel összesen", "shippingTotal": "Szállítás <PERSON>zes<PERSON>", "discountTotal": "Kedvezmény összesen", "taxTotalIncl": "<PERSON><PERSON> (beleértve)", "itemSubtotal": "Tétel részösszeg", "shippingSubtotal": "Szállítás részösszeg", "discountSubtotal": "Kedvezmény részösszeg", "taxTotal": "<PERSON><PERSON>"}, "transfer": {"title": "<PERSON><PERSON><PERSON>", "requestSuccess": "Rendelésátadási kérelem elküldve: {{email}}.", "currentOwner": "<PERSON><PERSON><PERSON><PERSON>", "newOwner": "<PERSON><PERSON>", "currentOwnerDescription": "A rendelés jelenlegi tulajdonosa.", "newOwnerDescription": "A rendelés új t<PERSON>."}, "payment": {"title": "<PERSON><PERSON><PERSON><PERSON>", "isReadyToBeCaptured": "A <0/> fize<PERSON>s készen áll a beszedésre.", "totalPaidByCustomer": "Vásárló <PERSON>l teljesített összeg", "capture": "Beszedés", "capture_short": "Beszedés", "refund": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markAsPaid": "Megjelölés <PERSON>", "statusLabel": "<PERSON>ze<PERSON><PERSON> s<PERSON>", "statusTitle": "<PERSON>ze<PERSON><PERSON> s<PERSON>", "status": {"notPaid": "<PERSON><PERSON>", "authorized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partiallyAuthorized": "<PERSON><PERSON><PERSON><PERSON> en<PERSON><PERSON>", "awaiting": "Várakozik", "captured": "Beszedve", "partiallyRefunded": "Részben visszatérítve", "partiallyCaptured": "Részben beszedve", "refunded": "Visszatérítve", "canceled": "Törölve", "requiresAction": "Intézkedést igényel"}, "capturePayment": "A(z) {{amount}} összeg beszedésre kerül.", "capturePaymentSuccess": "{{amount}} si<PERSON><PERSON>n beszedve", "markAsPaidPayment": "{{amount}} fizetettként lesz megjelölve", "markAsPaidPaymentSuccess": "{{amount}} si<PERSON><PERSON>n megje<PERSON>ö<PERSON> fizetettként", "createRefund": "Visszatérítés létrehozása", "refundPaymentSuccess": "{{amount}} si<PERSON><PERSON><PERSON> v<PERSON>", "createRefundWrongQuantity": "A mennyiség egy 1 és {{number}} köz<PERSON><PERSON> s<PERSON> le<PERSON>t", "refundAmount": "{{ amount }} v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentLink": "Fizetési link másolása {{ amount }}", "selectPaymentToRefund": "Válassza ki a visszatérítendő fizetést"}, "edits": {"title": "Rendelés szerkesztése", "confirm": "Szerkesztés megerősítése", "confirmText": "Egy rendelés szerkesztésének a megerősítésére készül. Ez a művelet nem vonható v<PERSON>za.", "cancel": "Szerkesztés megszakítása", "currentItems": "<PERSON><PERSON><PERSON><PERSON>", "currentItemsDescription": "A rendelés jelenlegi tételeinek szerkesztése.", "addItemsDescription": "Tov<PERSON><PERSON><PERSON> tételek hozzáadása a rendeléshez.", "addItems": "Tételek hozzáadása", "amountPaid": "Fizetett összeg", "newTotal": "Új végösszeg", "differenceDue": "Különbözet", "create": "Rendelés szerkesztése", "currentTotal": "Jelenlegi végösszeg", "noteHint": "Belső megjegyzés hozzáadása a rendeléshez", "cancelSuccessToast": "Rendelés szerkesztése megszakítva", "createSuccessToast": "Rendelés sikeresen szerkesztve", "activeChangeError": "A rendelésen már van aktív rendelésmódosítás (visszaküldés, reklamáció, csere stb.). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fejezze be vagy törölje a módosítást a megrendelés szerkesztése előtt.", "panel": {"title": "Rendelésszerkesztés kérve", "titlePending": "Rendelésszerkesztés folyamatban"}, "toast": {"canceledSuccessfully": "Rendelésszerkesztés megszakítva", "confirmedSuccessfully": "Rendelésszerkesztés megerősítve"}, "validation": {"quantityLowerThanFulfillment": "A mennyiség nem lehet kisebb, mint a teljesített mennyiség."}}, "edit": {"email": {"title": "<PERSON><PERSON>", "requestSuccess": "Email frissítve: {{email}}."}, "shippingAddress": {"title": "Szállítási cím szerkesztése", "requestSuccess": "Szállítási cím frissítve."}, "billingAddress": {"title": "Számlázási cím szerkesztése", "requestSuccess": "Számlázási cím frissítve."}}, "returns": {"create": "Visszaküldés létrehozása", "confirm": "Visszaküldés megerősítése", "confirmText": "<PERSON><PERSON> v<PERSON>zaküldés megerősítésére készül. Ez a művelet nem vonható vissza.", "inbound": "Bejö<PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON>", "sendNotification": "Értesítés küldése", "sendNotificationHint": "Értesítés küldése a vásárlónak a visszaküldésről.", "returnTotal": "Visszaküldés összesen", "inboundTotal": "Bejövő összesen", "estDifference": "Becsült különbözet", "outstandingAmount": "Fennmaradó <PERSON>", "reason": "Indok", "reasonHint": "Válasszon egy indokot a visszaküldéshez.", "note": "Megjegyzés", "noInventoryLevel": "<PERSON><PERSON><PERSON>", "noInventoryLevelDesc": "A kiválasztott helyszín nem rendelkezik a kiválasztott tételek készletszintjével. A visszaküldés kérhető, de nem fogadható be, amíg a kiválasztott helyhez nem hoznak létre készletszintet.", "noteHint": "Belső megjegyzés hozzáadása a visszaküldéshez.", "location": "<PERSON><PERSON><PERSON><PERSON>", "locationHint": "Válasszon egy he<PERSON> a visszaküldéshez.", "inboundShipping": "Visszaküldés szállítási módja", "inboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, hogyan szer<PERSON><PERSON><PERSON> v<PERSON> a termékeket.", "returnableQuantityLabel": "Visszaküldendő mennyiség", "refundableAmountLabel": "Visszatérítendő összeg", "returnRequestedInfo": "{{requestedItemsCount}}x tétel visszaküldése kérelmezve", "returnReceivedInfo": "{{requestedItemsCount}}x tétel érkeztetve", "itemReceived": "Tétel érkeztetve", "returnRequested": "Visszaküldés kérelmezve", "damagedItemReceived": "Sérült tétel érkezett", "damagedItemsReturned": "{{quantity}}x s<PERSON>r<PERSON>lt tétel visszaküldve", "activeChangeError": "A rendelésen már van aktív rendelésmódosítás (visszaküldés, reklamáció, csere stb.). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fejezze be vagy törölje a módosítást a visszaküldés kezdeményezése előtt.", "cancel": {"title": "Visszaküldés me<PERSON>zakítása", "description": "Biztosan megszakítja a visszaküldést?"}, "placeholders": {"noReturnShippingOptions": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>tőség", "hint": "A helyszínhez nem hoztak létre visszaszállítási lehetőségeket. Létrehozhat egyet a <LinkComponent>He<PERSON><PERSON><PERSON> és szállítás</LinkComponent> alatt."}, "outboundShippingOptions": {"title": "<PERSON><PERSON><PERSON> k<PERSON> szállítási lehetőség", "hint": "A helyszínhez nem hoztak létre kimenő szállítási lehetőségeket. Létrehozhat egyet a <LinkComponent>He<PERSON><PERSON><PERSON> és szállítás</LinkComponent> alatt."}}, "receive": {"action": "Tételek fogadása", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Összes tétel visszatöltése", "itemsLabel": "Fogadott tételek", "title": "Tételek fogadása a(z) #{{returnId}} számú visszaküldéshez", "sendNotificationHint": "Értesítés küldése a vásárlónak a fogadott tételekről.", "inventoryWarning": "A készletek automatikusan frissülnek a fogadott tételek alapján.", "writeOffInputLabel": "<PERSON><PERSON>y tétel s<PERSON>ült?", "toast": {"success": "Tételek si<PERSON>esen <PERSON>ad<PERSON>", "errorLargeValue": "A fogadott tétel mennyisége meghaladja a visszaküldött tétel mennyiségét.", "errorNegativeValue": "A fogadott tétel mennyisége nem lehet negatív.", "errorLargeDamagedValue": "A sérült tételek mennyisége + a kapott nem sérült tételek mennyisége meghaladja a visszaküldött tételek teljes mennyiségét. K<PERSON><PERSON><PERSON><PERSON><PERSON>, csökkentse a nem sérült tételek mennyiségét."}}, "toast": {"canceledSuccessfully": "Visszaküldés megszakítva", "confirmedSuccessfully": "Visszaküldés megerősítve"}, "panel": {"title": "Visszaküldés kezdeményezve", "description": "<PERSON> egy nyitott visszaküld<PERSON><PERSON> kérelme, amelyet be kell fejezni"}}, "claims": {"create": "Igénylés létrehozása", "confirm": "Igénylés megerősítése", "confirmText": "Egy igénylés megerősítésére készül. Ez a művelet nem vonható v<PERSON>za.", "manage": "Igénylés k<PERSON>e", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x hozzáadva igénylés útján", "outboundTotal": "Kimenő összesen", "outboundShipping": "Kimenő szállítás", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, mely<PERSON> módszert szeretné <PERSON>.", "refundAmount": "Becsült különbség", "activeChangeError": "A rendelésen már van aktív rendelésmódosítás. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fejezze be vagy törölje az előző módosítást.", "actions": {"cancelClaim": {"successToast": "Igénylés si<PERSON>n me<PERSON>zakítva."}}, "cancel": {"title": "Igénylés <PERSON>í<PERSON>", "description": "Biztosan meg akarja s<PERSON>tani az igénylést?"}, "tooltips": {"onlyReturnShippingOptions": "Ez a lista csak visszaszállítási lehetőségeket fog tartalmazni."}, "toast": {"canceledSuccessfully": "Igénylés si<PERSON> me<PERSON>", "confirmedSuccessfully": "Igénylés si<PERSON>esen megerősítve"}, "panel": {"title": "Igénylés kez<PERSON>ezve", "description": "<PERSON> egy nyitott igénylési kérelem, amelyet be kell fejezni"}}, "exchanges": {"create": "Csere létrehozása", "manage": "Csere kez<PERSON>e", "confirm": "Csere megerősítése", "confirmText": "Egy csere megerősítésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "outbound": "<PERSON><PERSON><PERSON>", "outboundItemAdded": "{{itemsCount}}x hozzáadva csere útján", "outboundTotal": "Kimenő összesen", "outboundShipping": "Kimenő szállítás", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, mely<PERSON> módszert szeretné <PERSON>.", "refundAmount": "Becsült különbség", "activeChangeError": "A rendelésen már van aktív rendelésmódosítás. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fejezze be vagy törölje az előző módosítást.", "actions": {"cancelExchange": {"successToast": "A csere sikeresen megszakítva."}}, "cancel": {"title": "Csere me<PERSON>zakí<PERSON>ás<PERSON>", "description": "Biztosan meg akarja <PERSON> a cser<PERSON>?"}, "tooltips": {"onlyReturnShippingOptions": "Ez a lista csak visszaszállítási lehetőségeket fog tartalmazni."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> si<PERSON>n me<PERSON>", "confirmedSuccessfully": "Csere sikeresen megerősítve"}, "panel": {"title": "<PERSON><PERSON>e kezde<PERSON>ezve", "description": "<PERSON> egy nyi<PERSON>tt csere k<PERSON>, amelyet be kell fejezni"}}, "reservations": {"allocatedLabel": "Kiosztva", "notAllocatedLabel": "<PERSON><PERSON>s kiosztva"}, "allocateItems": {"action": "Tételek kiosztása", "title": "Rendelési tételek kiosztása", "locationDescription": "<PERSON><PERSON><PERSON><PERSON> ki, mely<PERSON>színről szeretne kiosztani.", "itemsToAllocate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemsToAllocateDesc": "Válassza ki a kiosztani kívánt tételek számát", "search": "Tételek keresése", "consistsOf": "{{num}}x készletelemből áll", "requires": "{{num}} szükséges variánsonként", "toast": {"created": "Tételek sikeresen kiosztva"}, "error": {"quantityNotAllocated": "Vannak ki nem osztott tételek."}}, "shipment": {"title": "Teljesítés megjelölése szállítottként", "trackingNumber": "Nyomkövetési szám", "addTracking": "Nyomkövetési szám hozzáadása", "sendNotification": "Értesítés küldése", "sendNotificationHint": "Értesítse a vásárlót erről a szállításról.", "toastCreated": "Szállítás si<PERSON>esen létrehozva."}, "fulfillment": {"cancelWarning": "Egy teljesítés törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "markAsDeliveredWarning": "Egy teljesítést készül megjelölni kézbesítettként. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "differentOptionSelected": "A kiválasztott szállítási opció eltér a vásárló által kiválasztottól.", "disabledItemTooltip": "A kiválasztott szállítási opció nem teszi lehetővé ennek a tételnek a teljesítését", "unfulfilledItems": "Teljesítetlen tételek", "statusLabel": "Teljesítési státusz", "statusTitle": "Teljesítési státusz", "fulfillItems": "Tételek teljesítése", "awaitingFulfillmentBadge": "Teljesítésre vár", "requiresShipping": "Szállítást igényel", "number": "Teljesítés #{{number}}", "itemsToFulfill": "Teljesítendő tételek", "create": "Teljesítés létrehozása", "available": "Elérhető", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "markAsShipped": "Megjelölés s<PERSON>ll<PERSON>", "markAsPickedUp": "Meg<PERSON><PERSON><PERSON><PERSON><PERSON>", "markAsDelivered": "Megjelölés kézbesítettként", "itemsToFulfillDesc": "Válassza ki a teljesítendő tételeket és mennyiségeket", "locationDescription": "<PERSON><PERSON><PERSON><PERSON> ki, me<PERSON><PERSON>ínről szeretné teljesíteni a tételeket.", "sendNotificationHint": "Értesítse a vásárlókat a létrehozott teljesítésről.", "methodDescription": "Válasszon egy másik szállítási módszert, mint amit a vásárló választott", "error": {"wrongQuantity": "Csak egy tétel érhető el teljesítésre", "wrongQuantity_other": "A mennyiségnek 1 és {{number}} közötti számnak kell lennie", "noItems": "Nincsenek teljesítendő tételek.", "noShippingOption": "Szállítási opció szükséges", "noLocation": "<PERSON><PERSON><PERSON><PERSON> szükséges"}, "status": {"notFulfilled": "<PERSON><PERSON>", "partiallyFulfilled": "Részben teljesített", "fulfilled": "Teljesített", "partiallyShipped": "Részben sz<PERSON>llí<PERSON>tt", "shipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delivered": "Kézbesített", "partiallyDelivered": "Részben kézbesített", "partiallyReturned": "Részben visszaküldött", "returned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Intézkedést igényel"}, "toast": {"created": "Teljesítés si<PERSON>n létrehozva", "canceled": "Teljesítés si<PERSON>esen törölve", "fulfillmentShipped": "<PERSON><PERSON> le<PERSON>t tö<PERSON>ln<PERSON> egy már s<PERSON>llí<PERSON>tt teljesítést", "fulfillmentDelivered": "Teljesítés sikeresen megjelölve kézbesítettként", "fulfillmentPickedUp": "Teljesítés si<PERSON>esen megjelölve átvettként"}, "trackingLabel": "Nyomkövetés", "shippingFromLabel": "Szállít<PERSON> innen", "itemsLabel": "Tételek"}, "refund": {"title": "Visszatérítés létrehozása", "sendNotificationHint": "Értesítse a vásárlókat a létrehozott visszatérítésről.", "systemPayment": "Rendszerfizetés", "systemPaymentDesc": "Az Ön fizetései közül egy vagy több rendszerfizetés. Vegye figyelembe, hogy a beszedéseket és visszatérítéseket a Medusa nem kezeli az ilyen fizetések esetében.", "error": {"amountToLarge": "<PERSON><PERSON> le<PERSON>t többet v<PERSON>, mint az eredeti rendelési összeg.", "amountNegative": "A visszatérítési összegnek pozitív számnak kell lennie.", "reasonRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> egy v<PERSON>zatérí<PERSON><PERSON> okot."}}, "customer": {"contactLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editEmail": "<PERSON><PERSON>", "transferOwnership": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editBillingAddress": "Számlázási cím szerkesztése", "editShippingAddress": "Szállítási cím szerkesztése"}, "activity": {"header": "Tevékenység", "showMoreActivities_one": "{{count}} to<PERSON><PERSON><PERSON><PERSON> te<PERSON>g megjeleníté<PERSON>", "showMoreActivities_other": "{{count}} to<PERSON><PERSON><PERSON><PERSON> te<PERSON>g megjeleníté<PERSON>", "comment": {"label": "Megjegyzés", "placeholder": "Hagyjon egy megjegyzést", "addButtonText": "Megjegyzés hozzáadása", "deleteButtonText": "Megjegyzés törlése"}, "from": "Tól", "to": "Ig", "events": {"common": {"toReturn": "Visszaküldendő", "toSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "placed": {"title": "<PERSON><PERSON><PERSON>", "fromSalesChannel": "{{salesChannel}} értékesítési csatornáról"}, "canceled": {"title": "Rendel<PERSON> tö<PERSON>ö<PERSON>"}, "payment": {"awaiting": "Fizetésre vár", "captured": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON>ze<PERSON>s tö<PERSON>", "refunded": "Fizetés v<PERSON>zatérítve"}, "fulfillment": {"created": "Tételek teljesítve", "canceled": "Teljesítés törölve", "shipped": "Tételek szállítva", "delivered": "Tételek kézbesítve", "items_one": "{{count}} tétel", "items_other": "{{count}} tétel"}, "return": {"created": "Visszaküldés #{{returnId}} kérelmezve", "canceled": "Visszaküldés #{{returnId}} törölve", "received": "Visszaküldés #{{returnId}} érkeztetve", "items_one": "{{count}} tétel visszaküldve", "items_other": "{{count}} tétel visszaküldve"}, "note": {"comment": "Megjegyzés", "byLine": "{{author}} által"}, "claim": {"created": "Igénylés #{{claimId}} kérelmezve", "canceled": "Igénylés #{{claimId}} törölve", "itemsInbound": "{{count}} tétel visszaküldendő", "itemsOutbound": "{{count}} t<PERSON><PERSON>"}, "exchange": {"created": "Csere #{{exchangeId}} kérelmezve", "canceled": "Csere #{{exchangeId}} törölve", "itemsInbound": "{{count}} tétel visszaküldendő", "itemsOutbound": "{{count}} t<PERSON><PERSON>"}, "edit": {"requested": "Rendelésszerkesztés #{{editId}} kérelmezve", "confirmed": "Rendelésszerkesztés #{{editId}} megerősítve"}, "transfer": {"requested": "Rendelésátadás #{{transferId}} kérelmezve", "confirmed": "Rendelésátadás #{{transferId}} megerősítve", "declined": "Rendelésátadás #{{transferId}} elutasítva"}, "update_order": {"shipping_address": "Szállítási cím frissítve", "billing_address": "Számlázási cím frissítve", "email": "<PERSON><PERSON>"}}}, "fields": {"displayId": "Megjelenítési azonosító", "refundableAmount": "Visszatérítendő összeg", "returnableQuantity": "Visszaküldendő mennyiség"}}, "draftOrders": {"domain": "Piszkozat Rendelések", "deleteWarning": "A(z) {{id}} számú piszkozat rendelés törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "paymentLinkLabel": "Fizetési link", "cartIdLabel": "<PERSON><PERSON><PERSON><PERSON>", "markAsPaid": {"label": "Megjelölés <PERSON>", "warningTitle": "Megjelölés <PERSON>", "warningDescription": "A piszkozat rendelést fizetettként készül megjelölni. Ez a művelet nem von<PERSON> v<PERSON>, és később nem lesz lehetőség a fizetés beszedésére."}, "status": {"open": "Nyitott", "completed": "Befejezett"}, "create": {"createDraftOrder": "Piszkozat rendelés létrehozása", "createDraftOrderHint": "Hozzon létre egy új piszkozat rendelést a rendelés részleteinek kezeléséhez, mi<PERSON><PERSON><PERSON> leadná.", "chooseRegionHint": "Válasszon régiót", "existingItemsLabel": "Létező tételek", "existingItemsHint": "Adjon hozzá létező termékeket a piszkozat rendeléshez.", "customItemsLabel": "<PERSON><PERSON><PERSON><PERSON>", "customItemsHint": "Adjon hozzá egyedi tételeket a piszkozat rendeléshez.", "addExistingItemsAction": "Létező tételek hozzáadása", "addCustomItemAction": "Egyedi tétel hozzáadása", "noCustomItemsAddedLabel": "Még nincsenek egyedi tételek hozzáadva", "noExistingItemsAddedLabel": "Még nincsenek létező tételek hozzáadva", "chooseRegionTooltip": "Először válasszon régiót", "useExistingCustomerLabel": "Létező vásárló has<PERSON>", "addShippingMethodsAction": "Szállítási módok hozzáadása", "unitPriceOverrideLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shippingOptionLabel": "Szállítási opció", "shippingOptionHint": "Válassza ki a szállítási opciót a piszkozat rendeléshez.", "shippingPriceOverrideLabel": "Szállítási ár felülírása", "shippingPriceOverrideHint": "Felülírja a szállítási árat a piszkozat rendeléshez.", "sendNotificationLabel": "Értesítés küldése", "sendNotificationHint": "Értesítést küld a vásárlónak, amikor a piszkozat rendelés létrejön."}, "validation": {"requiredEmailOrCustomer": "Email vagy vásárló szükséges.", "requiredItems": "Legalább egy tétel szükséges.", "invalidEmail": "Az email címnek érvényes email címnek kell lennie."}}, "stockLocations": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON> és Szállítás", "list": {"description": "Kezelje az áruház készlethelyszíneit és szállítási opcióit."}, "create": {"header": "K<PERSON>zle<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "hint": "A készlethelyszín egy fizikai hely, ahol a termékeket tárolják és szállítják.", "successToast": "A(z) {{name}} he<PERSON><PERSON><PERSON>."}, "edit": {"header": "Készlethelysz<PERSON> szerkesztése", "viewInventory": "K<PERSON>zlet megtekintése", "successToast": "A(z) {{name}} he<PERSON><PERSON><PERSON> si<PERSON>n frissítve."}, "delete": {"confirmation": "A(z) {{name}} készlethelyszín törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za."}, "fulfillmentProviders": {"header": "Teljesítési Szolgáltatók", "shippingOptionsTooltip": "Ez a legördülő lista csak azokat a szolgáltatókat fogja tartalmazni, am<PERSON><PERSON> engedélyezve vannak ehhez a helyszínhez. Adja hozzá őket a helyszínhez, ha a legördülő lista le van tiltva.", "label": "Kapcsolódó teljesítési szolgáltatók", "connectedTo": "{{count}} a(z) {{total}} teljesí<PERSON>si s<PERSON>g<PERSON>ltatóbó<PERSON> ka<PERSON>olódva", "noProviders": "Ez a készlethelyszín nem kapcsolódik egyetlen teljesítési szolgáltatóhoz sem.", "action": "Szolgáltatók csatlakoztatása", "successToast": "A készlethelyszín teljesítési szolgáltatói sikeresen frissítve."}, "fulfillmentSets": {"pickup": {"header": "Átvétel"}, "shipping": {"header": "Szállítás"}, "disable": {"confirmation": "Biztosan le szeretné tiltani a(z) {{name}}? Ez törli az összes kapcsolódó szolgáltatási zónát és szállítási opciót, és nem vonható v<PERSON>.", "pickup": "Az átvétel sikeresen letiltva.", "shipping": "A szállítás sikeresen letiltva."}, "enable": {"pickup": "Az átvétel sikeresen engedélyezve.", "shipping": "A szállítás si<PERSON>esen engedélyezve."}}, "sidebar": {"header": "Szállítási Konfiguráció", "shippingProfiles": {"label": "Szállítási Profilok", "description": "Csoportosítsa a termékeket szállítási követelmények szerint"}}, "salesChannels": {"header": "Értékesítési Csatornák", "hint": "<PERSON>zelje azokat az értékesítési csatornákat, am<PERSON><PERSON> ehhez a helyszínhez kapcsolódnak.", "label": "Ka<PERSON><PERSON>ol<PERSON><PERSON>ó értékesítési csatornák", "connectedTo": "{{count}} a(z) {{total}} értékesítési c<PERSON>orná<PERSON>ó<PERSON>olódva", "noChannels": "A helyszín nem kapcsolódik egyetlen értékesítési csatornához sem.", "action": "Értékesítési csatornák csatlakoztatása", "successToast": "Az értékesítési csatornák sikeresen frissítve."}, "pickupOptions": {"edit": {"header": "Átvételi Opció Szerkesztése"}}, "shippingOptions": {"create": {"shipping": {"header": "Szállítási Opció Létrehozása a(z) {{zone}} számára", "hint": "Hozzon létre egy új szállít<PERSON>i opciót, hogy <PERSON>, ho<PERSON><PERSON> sz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a termékeket erről a helyszínről.", "label": "Szállítási opciók", "successToast": "A(z) {{name}} szállítási opció sikeresen létrehozva."}, "pickup": {"header": "Átvételi Opció Létrehozása a(z) {{zone}} számára", "hint": "Hozzon létre egy új átv<PERSON>tel<PERSON>, hogy <PERSON>, hogyan veszik <PERSON>t a termékeket erről a helyszínről.", "label": "Átvételi opciók", "successToast": "A(z) {{name}} átvételi opció sikeresen létrehozva."}, "returns": {"header": "Visszaküldési Opció Létrehozása a(z) {{zone}} számára", "hint": "Hozzon létre egy új v<PERSON><PERSON><PERSON>, hogy <PERSON>, hogyan küldik vissza a termékeket erre a helyszínre.", "label": "Visszaküldési opciók", "successToast": "A(z) {{name}} visszaküldési opció sikeresen létrehozva."}, "tabs": {"details": "Részletek", "prices": "<PERSON><PERSON>"}, "action": "<PERSON>ció létrehoz<PERSON>"}, "delete": {"confirmation": "A(z) {{name}} szállítási opció törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "successToast": "A(z) {{name}} szállítási opció sikeresen törölve."}, "edit": {"header": "Szállítási Opció Szerkesztése", "action": "<PERSON><PERSON><PERSON>keszté<PERSON>", "successToast": "A(z) {{name}} szállítási opció sikeresen frissítve."}, "pricing": {"action": "<PERSON><PERSON>"}, "conditionalPrices": {"header": "Feltételes Árak a(z) {{name}} számára", "description": "Kezelje a feltételes árakat ehhez a szállítási opcióhoz a kosár tétel összesen alapján.", "attributes": {"cartItemTotal": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> összesen"}, "summaries": {"range": "Ha <0>{{attribute}}</0> <PERSON><PERSON><PERSON><PERSON> <1>{{gte}}</1> és <2>{{lte}}</2> <PERSON><PERSON><PERSON><PERSON><PERSON> van", "greaterThan": "Ha <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Ha <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON>", "manageConditionalPrices": "Feltételes árak k<PERSON>e"}, "rules": {"amount": "Szállítási opció ára", "gte": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> t<PERSON>tel összesen", "lte": "<PERSON><PERSON><PERSON> k<PERSON> összesen"}, "customRules": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Ez a feltételes ár olyan s<PERSON> tartalmaz, amelyeket nem lehet kezelni a vezérlőpulton.", "eq": "<PERSON><PERSON><PERSON>r tétel összesen egyenlőnek kell lennie", "gt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>tel összesen nagyobbnak kell lennie", "lt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>tel összesen kisebbnek kell lennie"}, "errors": {"amountRequired": "Szállítási opció ára szükséges", "minOrMaxRequired": "Legalább egy minimális vagy maximális kosár tétel összesen megadása szükséges", "minGreaterThanMax": "A minimális kosár tétel összesen kisebbnek vagy egyenlőnek kell lennie a maximális kosár tétel összesen értékkel", "duplicateAmount": "Szállítási opció ára egyedinek kell lennie minden feltételhez", "overlappingConditions": "A feltételeknek egyedinek kell lenniük az összes ár sza<PERSON>"}}, "fields": {"count": {"shipping_one": "{{count}} szállítási opció", "shipping_other": "{{count}} szállítási opciók", "pickup_one": "{{count}} átvé<PERSON><PERSON> opci<PERSON>", "pickup_other": "{{count}} átvétel<PERSON> op<PERSON>", "returns_one": "{{count}} v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> opció", "returns_other": "{{count}} v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> opciók"}, "priceType": {"label": "<PERSON><PERSON>", "options": {"fixed": {"label": "Fix", "hint": "A szállítási opció ára fix, és nem változik a rendelés tartalma alapján."}, "calculated": {"label": "Számí<PERSON>tt", "hint": "A szállítási opció ára a teljesítési szolgáltató által kerül kiszámításra a fizetés során."}}}, "enableInStore": {"label": "Engedélyezés az áruházban", "hint": "Engedé<PERSON><PERSON><PERSON>, hogy a vásárlók használhassák ezt az opciót a fizetés során."}, "provider": "Teljesítési szolgáltató", "profile": "Szállítási profil", "fulfillmentOption": "Teljesítési opció"}}, "serviceZones": {"create": {"headerPickup": "Szolgáltatási Zóna Létrehozása Átvételhez a(z) {{location}} helyszínen", "headerShipping": "Szolgáltatási Zóna Létrehozása Szállításhoz a(z) {{location}} helyszínen", "action": "Szolgáltatási zóna létrehozása", "successToast": "A(z) {{name}} szolgáltatási zóna si<PERSON>esen létrehozva."}, "edit": {"header": "Szolgáltatási Zóna Szerkesztése", "successToast": "A(z) {{name}} szolgáltatási zóna si<PERSON>esen frissítve."}, "delete": {"confirmation": "A(z) {{name}} szolgáltatási zóna törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>.", "successToast": "A(z) {{name}} szolgáltatási zóna si<PERSON>esen törölve."}, "manageAreas": {"header": "Területek Kezelése a(z) {{name}} számára", "action": "Területek kezelése", "label": "Területek", "hint": "Válassza ki azokat a földrajzi területeket, amelyeket a szolgáltatási zóna lefed.", "successToast": "A(z) {{name}} területei sikeresen frissítve."}, "fields": {"noRecords": "<PERSON><PERSON><PERSON><PERSON> szolgáltatási zónák, amelyekhez szállítási opciókat lehetne hozzáadni.", "tip": "A szolgáltatási zóna földrajzi zónák vagy területek gyűjteménye. <PERSON><PERSON>, hogy korlátozzák a rendelkezésre álló szállítási opciókat egy meghatározott helyszínre."}}}, "shippingProfile": {"domain": "Szállítási Profilok", "subtitle": "Csoportosítsa a termékeket hasonló szállítási követelmények szerint profilokba.", "create": {"header": "Szállítási Profil Létrehozása", "hint": "Hozzon létre egy új szállítási profilt, hogy csoportosítsa a termékeket hasonló szállítási követelmények szerint.", "successToast": "A(z) {{name}} szállítási profil si<PERSON>."}, "delete": {"title": "Szállítási Profil Törlése", "description": "A(z) {{name}} szállítási profil törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>.", "successToast": "A(z) {{name}} szállítási profil si<PERSON>esen tö<PERSON>."}, "tooltip": {"type": "Adja meg a szállítási profil <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, stb."}}, "taxRegions": {"domain": "Adórégiók", "list": {"hint": "<PERSON><PERSON><PERSON>, hogy mit számít fel a vás<PERSON><PERSON><PERSON>k, amikor különbö<PERSON><PERSON> országokból és régiókból vásárolnak."}, "delete": {"confirmation": "Egy adórégió törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "successToast": "<PERSON>z adórégió si<PERSON>n tö<PERSON>ö<PERSON>."}, "create": {"header": "Adórégió <PERSON>", "hint": "Hozzon létre egy új adó<PERSON>gi<PERSON>t, hogy meghatározza az adókulcsokat egy adott ország számára.", "errors": {"rateIsRequired": "Adó<PERSON><PERSON><PERSON> szükséges, amikor alapértelmezett adókulcsot hoz létre.", "nameIsRequired": "Név s<PERSON>ü<PERSON>, amikor alapértelmezett adókulcsot hoz létre."}, "successToast": "<PERSON>z adórégió <PERSON> lé<PERSON>hoz<PERSON>."}, "province": {"header": "Tartományok", "create": {"header": "Tartomány Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégi<PERSON>t, hogy meghatározza az adókulcsokat egy adott tartomány számára."}}, "state": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Állam Adórégió Létreho<PERSON>", "hint": "Hozzon létre egy új adórégi<PERSON>t, hogy meghatározza az adókulcsokat egy adott állam számára."}}, "stateOrTerritory": {"header": "Államok vagy <PERSON>", "create": {"header": "Állam/<PERSON>rület Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégi<PERSON>t, hogy meghatározza az adókulcsokat egy adott <PERSON>/ter<PERSON><PERSON> számára."}}, "county": {"header": "Megyék", "create": {"header": "Megye Adórégió <PERSON>", "hint": "Hozzon létre egy új adórégi<PERSON>t, hogy meghatározza az adókulcsokat egy adott megye számára."}}, "region": {"header": "Régiók", "create": {"header": "<PERSON><PERSON><PERSON><PERSON>é<PERSON>", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott régió s<PERSON>á<PERSON>."}}, "department": {"header": "Osztályok", "create": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Hozzon létre egy új adó<PERSON>gi<PERSON>, hogy meghatározza az adókulcsokat egy adott oszt<PERSON>ára."}}, "territory": {"header": "Területek", "create": {"header": "<PERSON><PERSON><PERSON><PERSON> Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott terület számára."}}, "prefecture": {"header": "Prefektúrák", "create": {"header": "Prefektúra Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott prefektúra számára."}}, "district": {"header": "Kerületek", "create": {"header": "<PERSON><PERSON><PERSON><PERSON> Adórégió Létrehozás<PERSON>", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott kerület számára."}}, "governorate": {"header": "Kormányzóságok", "create": {"header": "Kormányzóság Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott kormányzóság számára."}}, "canton": {"header": "Kantonok", "create": {"header": "<PERSON><PERSON>gi<PERSON>", "hint": "Hozzon létre egy új adórégi<PERSON>t, hogy meghatározza az adókulcsokat egy adott kanton számára."}}, "emirate": {"header": "Emírségek", "create": {"header": "Emírség Adórégió Létrehozása", "hint": "Hozzon létre egy új adórégiót, hogy meghatározza az adókulcsokat egy adott emírség számára."}}, "sublevel": {"header": "Alszintek", "create": {"header": "<PERSON><PERSON><PERSON>étrehoz<PERSON>", "hint": "Hozzon létre egy új adó<PERSON>gi<PERSON>, hogy meghatározza az adókulcsokat egy adott al<PERSON>int s<PERSON>mára."}}, "taxOverrides": {"header": "Felülírások", "create": {"header": "Felülírás <PERSON>", "hint": "Hozzon létre egy ad<PERSON>, amely f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> az alapértelmezett adókulcsokat a kiválasztott feltételekhez."}, "edit": {"header": "Fe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Szerkessze az ad<PERSON><PERSON><PERSON>, amely felülírja az alapértelmezett adókulcsokat a kiválasztott feltételekhez."}}, "taxRates": {"create": {"header": "Adókulcs Létrehozása", "hint": "Hozzon létre egy új ad<PERSON>ot, hogy meghatározza az adókulcsot egy régió s<PERSON>.", "successToast": "Az adókulcs sikeresen létrehozva."}, "edit": {"header": "Adókulcs Szerkesztése", "hint": "Szerkessze az adókulcsot, hogy meghatározza az adókulcsot egy régió s<PERSON>má<PERSON>.", "successToast": "Az adókulcs sikeresen frissítve."}, "delete": {"confirmation": "A(z) {{name}} adókulcs törlésére készül. Ez a művelet nem vonható v<PERSON>.", "successToast": "Az adókulcs sikeresen törölve."}}, "fields": {"isCombinable": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Ez az adókulcs kombinálható-e az adórégió alapértelmezett kulcsával.", "true": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON>"}, "defaultTaxRate": {"label": "Alapértelmezett adó<PERSON>", "tooltip": "Az alapértelmezett adókulcs ehhez a régióhoz. P<PERSON>lda erre egy ország vagy régió standard ÁFA kulcsa.", "action": "Alapértelmezett adókulcs létrehozása"}, "taxRate": "Adókulcs", "taxCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targets": {"label": "Célok", "hint": "Válassza ki azokat a célo<PERSON>, amelyekre ez az adókulcs vonatkozik.", "options": {"product": "Termékek", "productCollection": "Termékgyűjtemények", "productTag": "Termékcímkék", "productType": "Terméktípusok", "customerGroup": "Vásárlói csoportok"}, "operators": {"in": "in", "on": "on", "and": "és"}, "placeholders": {"product": "Termékek keresése", "productCollection": "Termékgyűjtemények keresése", "productTag": "Termékcímkék keresése", "productType": "Terméktípusok keresése", "customerGroup": "Vásárlói csoportok keresése"}, "tags": {"product": "Termék", "productCollection": "Termékgyűjtemény", "productTag": "Termékcímke", "productType": "Terméktípus", "customerGroup": "Vásárlói csoport"}, "modal": {"header": "Célok hozzáadása"}, "values_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "values_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfTargets_one": "{{count}} cél", "numberOfTargets_other": "{{count}} c<PERSON><PERSON>k", "additionalValues_one": "és {{count}} to<PERSON><PERSON><PERSON><PERSON>", "additionalValues_other": "és {{count}} to<PERSON><PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON>"}, "sublevels": {"labels": {"province": "Tartomány", "state": "<PERSON><PERSON><PERSON>", "region": "<PERSON><PERSON><PERSON><PERSON>", "stateOrTerritory": "Állam/Terület", "department": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "county": "<PERSON><PERSON>", "territory": "<PERSON><PERSON><PERSON><PERSON>", "prefecture": "Prefektúra", "district": "<PERSON><PERSON><PERSON><PERSON>", "governorate": "Kormányzóság", "emirate": "Emírség", "canton": "<PERSON><PERSON>", "sublevel": "<PERSON><PERSON><PERSON> k<PERSON>d"}, "placeholders": {"province": "Tartomány kiválasztása", "state": "Állam kiválasztása", "region": "<PERSON><PERSON><PERSON><PERSON>", "stateOrTerritory": "Állam/Terület kiválasztása", "department": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "county": "Megye kiválasztása", "territory": "Terület kiválasztása", "prefecture": "Prefektúra kiválasztása", "district": "Ker<PERSON>let kiválasztása", "governorate": "Kormányzóság kiválasztása", "emirate": "Emírség kiválasztása", "canton": "<PERSON><PERSON> kiválasztása"}, "tooltips": {"sublevel": "Adja meg az ISO 3166-2 kódot az alszint adórégióhoz.", "notPartOfCountry": "{{province}} nem tűnik a(z) {{country}} részének. <PERSON>, <PERSON><PERSON>, hogy ez hely<PERSON>-e."}, "alert": {"header": "Az alszint régiók le vannak tiltva ehhez az adórégióhoz", "description": "Az alszint régiók alapértelmezés szerint le vannak tiltva ehhez a régióhoz. Engedélyezheti őket, hogy alszint régiókat hozzon létre, péld<PERSON><PERSON> tartományokat, államokat vagy területeket.", "action": "Alsó szintű régiók engedélyezése"}}, "noDefaultRate": {"label": "<PERSON><PERSON><PERSON> kulcs", "tooltip": "Ez az adórégió nem rendelkezik alapértelmezett adókulccsal. Ha van standard kulcs, p<PERSON>ld<PERSON><PERSON> egy ország ÁFA-ja, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja hozzá ehhez a régióhoz."}}}, "promotions": {"domain": "Promóciók", "sections": {"details": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"template": "<PERSON><PERSON><PERSON>", "details": "Részletek", "campaign": "Kampány"}, "fields": {"type": "<PERSON><PERSON><PERSON>", "value_type": "<PERSON>rt<PERSON>k T<PERSON>a", "value": "<PERSON><PERSON><PERSON><PERSON>", "campaign": "Kampány", "method": "<PERSON><PERSON><PERSON><PERSON>", "allocation": "Elosztás", "addCondition": "Feltétel hozzáadása", "clearAll": "Összes törlése", "amount": {"tooltip": "Válassza ki a pénznem kódját az összeg beállításának engedélyezéséhez"}, "conditions": {"rules": {"title": "Ki használhatja ezt a kódot?", "description": "Melyik vásárl<PERSON> hasz<PERSON> a promóciós kódot? A promóciós kódot minden vás<PERSON>rl<PERSON> hasz<PERSON>, ha érintetlenül hagyja."}, "target-rules": {"title": "<PERSON><PERSON> t<PERSON><PERSON>ek<PERSON> a promóció?", "description": "A promóció azokra a tételekre vonatkozik, am<PERSON><PERSON> megfelelnek az alábbi felt<PERSON>k."}, "buy-rules": {"title": "Mi szükséges a kosárban a promóció feloldásához?", "description": "Ha ezek a feltételek megfelelnek, engedélyezzük a promóciót a cél tételeken."}}}, "tooltips": {"campaignType": "A pénznem kódját ki kell választani a promócióban a költségvetési limit beállításához."}, "errors": {"requiredField": "Kötelező mező", "promotionTabError": "Javítsa ki a Promóciós <PERSON>, <PERSON><PERSON><PERSON><PERSON>"}, "toasts": {"promotionCreateSuccess": "A(z) {{code}} pro<PERSON><PERSON><PERSON><PERSON> si<PERSON> létrehozva."}, "create": {}, "edit": {"title": "<PERSON><PERSON><PERSON><PERSON>ó Részleteinek Szerkesztése", "rules": {"title": "Használati feltételek szerkesztése"}, "target-rules": {"title": "Tétel feltételek szerkesztése"}, "buy-rules": {"title": "Vásárlási szabályok szerkesztése"}}, "campaign": {"header": "Kampány", "edit": {"header": "Kampány Szerkesztése", "successToast": "A promóció kampánya sikeresen frissítve."}, "actions": {"goToCampaign": "Ugrás a kampányhoz"}}, "campaign_currency": {"tooltip": "Ez a promóció pénzneme. Módosítsa a Részletek fülön."}, "form": {"required": "Kötelező", "and": "ÉS", "selectAttribute": "Attribútum kiválasztása", "campaign": {"existing": {"title": "Létező <PERSON>", "description": "Adja ho<PERSON>á a promóciót egy meglévő kampányhoz.", "placeholder": {"title": "Nincsenek meglévő kampányok", "desc": "Létre<PERSON>z<PERSON> e<PERSON>, hogy nyomon kövesse több promóciót és beállítsa a költségvetési limiteket."}}, "new": {"title": "<PERSON><PERSON>", "description": "Hozzon létre egy új kampányt ehhez a promócióhoz."}, "none": {"title": "Kampány <PERSON>", "description": "<PERSON>oly<PERSON><PERSON>, hogy a promóciót kampányhoz társítaná"}}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "draft": {"title": "Piszkozat", "description": "A vásárlók még nem tudják használni a kódot"}, "active": {"title": "Aktív", "description": "A vásárlók használhatják a kódot"}, "inactive": {"title": "Inaktív", "description": "A vásárlók már nem tudják használni a kódot"}}, "method": {"label": "<PERSON><PERSON><PERSON><PERSON>", "code": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A vásárlóknak meg kell adniuk ezt a kódot a fizetés során"}, "automatic": {"title": "Automatikus", "description": "A vásárlók látni fogják ezt a promóciót a fizetés során"}}, "max_quantity": {"title": "<PERSON><PERSON><PERSON>", "description": "A tételek maximális mennyisége, amely<PERSON> ez a prom<PERSON><PERSON><PERSON>."}, "type": {"standard": {"title": "Standard", "description": "Egy standard promóció"}, "buyget": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Vásároljon X-et, kapjon Y promóciót"}}, "allocation": {"each": {"title": "Mindegyik", "description": "Értéket alkalmaz minden tételre"}, "across": {"title": "Keresztül", "description": "Értéket alkalmaz a tételeken keresztül"}}, "code": {"title": "<PERSON><PERSON><PERSON>", "description": "A kód, amelyet a vásárlók megadnak a fizetés során."}, "value": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "value_type": {"fixed": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A kedvezmény összeg. pl. 100"}, "percentage": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A kedvezmény százaléka. pl. 8%"}}}, "deleteWarning": "A(z) {{code}} promóció törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "createPromotionTitle": "Promóció <PERSON>", "type": "Promóció tí<PERSON>", "conditions": {"add": "Feltétel hozzáadása", "list": {"noRecordsMessage": "<PERSON><PERSON> hozz<PERSON> egy <PERSON>, ho<PERSON>, mely t<PERSON><PERSON><PERSON><PERSON> a promóció."}}}, "campaigns": {"domain": "Kampányok", "details": "<PERSON><PERSON><PERSON><PERSON>", "status": {"active": "Aktív", "expired": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>"}, "delete": {"title": "<PERSON><PERSON><PERSON> benne?", "description": "A(z) '{{name}}' kampány törlésére készül. Ez a művelet nem vonható v<PERSON>.", "successToast": "A(z) '{{name}}' ka<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>."}, "edit": {"header": "Kampány Szerkesztése", "description": "A kampány részleteinek szerkesztése.", "successToast": "A(z) '{{name}}' ka<PERSON><PERSON><PERSON> si<PERSON>esen frissítve."}, "configuration": {"header": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Kampány Konfigur<PERSON><PERSON>ó <PERSON>keszté<PERSON>", "description": "A kampány konfigurációjának szerkesztése.", "successToast": "A kampány konfigurációja sikeresen frissítve."}}, "create": {"title": "Kampány Létrehozása", "description": "Promóciós ka<PERSON>ány létrehozása.", "hint": "Promóciós ka<PERSON>ány létrehozása.", "header": "Kampány Létrehozása", "successToast": "A(z) '{{name}}' ka<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>."}, "fields": {"name": "Név", "identifier": "Azonosító", "start_date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "end_date": "Befejezési d<PERSON>", "total_spend": "Költségvetés elköltve", "total_used": "Költségvetés felhasználva", "budget_limit": "Költségvetési limit", "campaign_id": {"hint": "Csak azok a kampányok jelennek meg ebben a list<PERSON><PERSON>, amelyek pénznem kódja megegyezik a promócióval."}}, "budget": {"create": {"hint": "Hozzon létre költségvetést a kampányhoz.", "header": "Kampány Költségvetés"}, "details": "Kampány költségvetés", "fields": {"type": "<PERSON><PERSON><PERSON>", "currency": "Pénznem", "limit": "Limit", "used": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Költség", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy limitet az összes promóciós használat kedvezményes összegére."}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy limitet arra, hogy h<PERSON><PERSON><PERSON> has<PERSON> a promóció."}}, "edit": {"header": "Kampány Költségvetés Szerkesztése"}}, "promotions": {"remove": {"title": "Promóció eltávolítása a kampányból", "description": "{{count}} promóció(k) eltávolítására készül a kampányból. Ez a művelet nem vonható vissza."}, "alreadyAdded": "Ez a promóció már hozzá lett adva a kampányhoz.", "alreadyAddedDiffCampaign": "Ez a promóció már hozzá lett adva egy másik kampányhoz ({{name}}).", "currencyMismatch": "A promóció és a kampány pénzneme nem egyezik", "toast": {"success": "{{count}} prom<PERSON><PERSON><PERSON>(k) sikeresen hozz<PERSON>adva a kampányhoz"}, "add": {"list": {"noRecordsMessage": "Elősz<PERSON>r ho<PERSON>n létre egy promóciót."}}, "list": {"noRecordsMessage": "<PERSON><PERSON>senek promóciók a kampányban."}}, "deleteCampaignWarning": "A(z) {{name}} kampány törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Hozzon létre akciókat vagy felülírja az árakat meghatározott feltételekhez.", "delete": {"confirmation": "A(z) {{title}} árlista törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "successToast": "A(z) {{title}} <PERSON><PERSON><PERSON> si<PERSON>esen törö<PERSON>."}, "create": {"header": "Árlista Létrehozása", "subheader": "Hozzon létre egy ú<PERSON>, hogy kezelje a termékek árait.", "tabs": {"details": "Részletek", "products": "Termékek", "prices": "<PERSON><PERSON>"}, "successToast": "A(z) {{title}} <PERSON><PERSON><PERSON> si<PERSON>n létrehozva.", "products": {"list": {"noRecordsMessage": "Először hozzon létre egy terméket."}}}, "edit": {"header": "Árlista Szerkesztése", "successToast": "A(z) {{title}} árlista sikeresen frissítve."}, "configuration": {"header": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Árlista Konfigur<PERSON><PERSON>ó <PERSON>keszté<PERSON>", "description": "Az árlista konfigurációjának szerkesztése.", "successToast": "Az árlista konfigurációja sikeresen frissítve."}}, "products": {"header": "Termékek", "actions": {"addProducts": "Termékek hozzáadása", "editPrices": "<PERSON><PERSON>"}, "delete": {"confirmation_one": "{{count}} termék árainak törlésére készül az árlistában. Ez a művelet nem von<PERSON>ó v<PERSON>.", "confirmation_other": "{{count}} termék árainak törlésére készül az árlistában. Ez a művelet nem von<PERSON>ó v<PERSON>.", "successToast_one": "{{count}} termék árainak törlése sikeresen megtö<PERSON>t.", "successToast_other": "{{count}} termék árainak törlése sikeresen megtö<PERSON>t."}, "add": {"successToast": "<PERSON><PERSON> á<PERSON> si<PERSON>esen hozz<PERSON>adva az árlistához."}, "edit": {"successToast": "<PERSON><PERSON> si<PERSON>esen frissítve."}}, "fields": {"priceOverrides": {"label": "<PERSON><PERSON>", "header": "<PERSON><PERSON>"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"active": "Aktív", "draft": "Piszkozat", "expired": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON>, mi<PERSON><PERSON> t<PERSON>ne létreho<PERSON>ni.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>z ak<PERSON>ós árak ideiglenes árváltozások a termékekhez."}, "override": {"label": "Felülírás", "description": "A felülírásokat általában vásárló-specifikus árak létrehozására <PERSON>."}}}, "startsAt": {"label": "<PERSON><PERSON> van kezdési d<PERSON>?", "hint": "Ütemezze az árl<PERSON>, hogy a jövőben aktiválódjon."}, "endsAt": {"label": "<PERSON><PERSON> van lejárati d<PERSON>?", "hint": "Ütemezze az árl<PERSON>, hogy a jövőben deaktiválódjon."}, "customerAvailability": {"header": "Vásárlói csoportok kiválasztása", "label": "Vásárlói elérhetőség", "hint": "<PERSON><PERSON><PERSON><PERSON> ki, mely v<PERSON><PERSON><PERSON> csoportokra vonatkozzon az árlista.", "placeholder": "Vásárlói csoportok keresése", "attribute": "Vásárlói csoportok"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "<PERSON><PERSON>je profiljának részleteit.", "fields": {"languageLabel": "Nyelv", "usageInsightsLabel": "Használati betekintések"}, "edit": {"header": "<PERSON><PERSON>", "languageHint": "Az admin vezérlőpulton használni kívánt nyelv. Ez nem változtatja meg az áruház nyelvét.", "languagePlaceholder": "Nyelv kiválasztása", "usageInsightsHint": "Ossza meg a használati betekintéseket, és segítsen nekünk javítani a Medusát. További információ<PERSON>, hogy mit gyűjtünk és hogyan has<PERSON>, megtalálhat a <0>dokumentációban</0>."}, "toast": {"edit": "Profilváltozások mentve"}}, "users": {"domain": "Felhasználók", "editUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inviteUser": "Felhas<PERSON><PERSON><PERSON><PERSON>", "inviteUserHint": "Hívjon meg egy új felhasználót az áruházába.", "sendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingInvites": "Függőben lévő Meghívók", "deleteInviteWarning": "A(z) {{email}} meghívó törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "resendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyInviteLink": "Meghívó link másolása", "expiredOnDate": "<PERSON><PERSON><PERSON><PERSON> ekkor: {{date}}", "validFromUntil": "<PERSON><PERSON><PERSON><PERSON><PERSON> <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Elfogadva ekkor: {{date}}", "inviteStatus": {"accepted": "Elfogadva", "pending": "Függőben", "expired": "<PERSON><PERSON><PERSON><PERSON>"}, "roles": {"admin": "Admin", "developer": "Fejlesztő", "member": "Tag"}, "list": {"empty": {"heading": "Nincsenek felhasználók", "description": "Amint egy felhasz<PERSON>ó<PERSON> meghívtak, itt fog megjelenni."}, "filtered": {"heading": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>, amely meg<PERSON>l a jelenlegi szűrési kritériumoknak."}}, "deleteUserWarning": "A(z) {{name}} felhasználó törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "deleteUserSuccess": "A(z) {{name}} fel<PERSON>z<PERSON><PERSON><PERSON> si<PERSON>esen törölve", "invite": "Meghívás"}, "store": {"domain": "Áru<PERSON><PERSON><PERSON>", "manageYourStoresDetails": "<PERSON><PERSON><PERSON>ak részleteit", "editStore": "Áru<PERSON><PERSON><PERSON>té<PERSON>", "defaultCurrency": "Alapértelmezett pénznem", "defaultRegion": "Alapértelmezett ré<PERSON>", "defaultSalesChannel": "Alapértelmezett értékesítési c<PERSON>a", "defaultLocation": "Alapérte<PERSON><PERSON><PERSON>", "swapLinkTemplate": "Csere link sablon", "paymentLinkTemplate": "Fizetési link sablon", "inviteLinkTemplate": "Meghívó link sablon", "currencies": "Pénznemek", "addCurrencies": "Pénznemek hozzáadása", "enableTaxInclusivePricing": "<PERSON><PERSON><PERSON>ámított <PERSON> engedélyezése", "disableTaxInclusivePricing": "<PERSON><PERSON><PERSON> számított árak letiltás<PERSON>", "removeCurrencyWarning_one": "{{count}} pénznem eltávolítására készül az áruházból. Győződjön meg róla, hogy eltávolította az összes árat, amely ezt a pénznemet <PERSON>, miel<PERSON><PERSON> folytatná.", "removeCurrencyWarning_other": "{{count}} pénznemek eltávolítására készül az áruházból. Győződjön meg róla, hogy eltávolította az összes árat, amely ezeket a pénznemeket használja, miel<PERSON>tt folytatná.", "currencyAlreadyAdded": "A pénznem már hozzá lett adva az áruházhoz.", "edit": {"header": "Áruház <PERSON>té<PERSON>"}, "toast": {"update": "<PERSON><PERSON><PERSON><PERSON><PERSON>n frissítve", "currenciesUpdated": "Pénznemek sikeresen frissítve", "currenciesRemoved": "Pénznemek sikeresen eltávolítva az áruházból", "updatedTaxInclusivitySuccessfully": "<PERSON><PERSON><PERSON> s<PERSON>ámított árak si<PERSON>esen frissítve"}}, "regions": {"domain": "Régiók", "subtitle": "A régió egy olyan terület, ahol termékeket értékesít. Több országot is lefedhet, és különböző adókulcsokkal, szolgáltatókkal és pénznemekkel rendelkezik.", "createRegion": "<PERSON><PERSON><PERSON><PERSON>", "createRegionHint": "Kezelje az adókulcsokat és szolgáltatókat egy országcsoport számára.", "addCountries": "Országok hozzáadása", "editRegion": "<PERSON><PERSON><PERSON><PERSON>", "countriesHint": "<PERSON><PERSON> ho<PERSON> azokat az országokat, am<PERSON><PERSON> ebbe a régióba tartoznak.", "deleteRegionWarning": "A(z) {{name}} r<PERSON>gió törlésére készül. Ez a művelet nem vonható vissza.", "removeCountriesWarning_one": "{{count}} or<PERSON><PERSON><PERSON> eltávolítására készül a régióból. Ez a művelet nem vonható v<PERSON>za.", "removeCountriesWarning_other": "{{count}} országok eltávolítására készül a régióból. Ez a művelet nem vonható v<PERSON>za.", "removeCountryWarning": "A(z) {{name}} or<PERSON><PERSON><PERSON> eltávolítására k<PERSON>zül a régióból. Ez a művelet nem vonható v<PERSON>za.", "automaticTaxesHint": "Ha engedély<PERSON><PERSON> van, az adók csak a fizetés során kerülnek kiszámításra a szállítási cím alapján.", "taxInclusiveHint": "<PERSON> engedé<PERSON><PERSON><PERSON> van, a régióban az árak adóval együtt számítottak lesznek.", "providersHint": "<PERSON><PERSON>, hogy mely fizetési szolgáltatók érhetők el ebben a régióban.", "shippingOptions": "Szállítási Opciók", "deleteShippingOptionWarning": "A(z) {{name}} szállítási opció törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON>", "priceType": "<PERSON><PERSON>", "flatRate": "<PERSON>x <PERSON>", "calculated": "Számí<PERSON>tt", "list": {"noRecordsMessage": "Hozzon létre egy régiót az értékesítési területekhez."}, "toast": {"delete": "<PERSON><PERSON><PERSON><PERSON> tö<PERSON>", "edit": "Régió s<PERSON>kesztése mentve", "create": "<PERSON><PERSON><PERSON><PERSON>", "countries": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n frissítve"}, "shippingOption": {"createShippingOption": "Szállítási Opció Létrehozása", "createShippingOptionHint": "Hozzon létre egy új szállítási opciót a régióhoz.", "editShippingOption": "Szállítási Opció Szerkesztése", "fulfillmentMethod": "Teljesítési <PERSON>", "type": {"outbound": "<PERSON><PERSON><PERSON>", "outboundHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ha szállítási opciót hoz létre a termékek vásárlóhoz történő küldéséhez.", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnHint": "Hasz<PERSON><PERSON><PERSON>, ha visszaküldési opciót hoz létre a termékek vásárlótól történő visszaküldéséhez."}, "priceType": {"label": "<PERSON><PERSON>", "flatRate": "Fix díj", "calculated": "Számí<PERSON>tt"}, "availability": {"adminOnly": "Csak adminisztrátor", "adminOnlyHint": "<PERSON> engedély<PERSON><PERSON> van, a szállítási opció csak az admin vezérlőpulton érhető el, és nem az áruházban."}, "taxInclusiveHint": "<PERSON> engedé<PERSON><PERSON><PERSON> van, a szállítási opció ára adóval együtt számított lesz.", "requirements": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Adja meg a szállítási opció követelményeit."}}}, "taxes": {"domain": "Adórégiók", "domainDescription": "<PERSON><PERSON><PERSON>ó<PERSON>", "countries": {"taxCountriesHint": "Az adóbeállítások az itt felsorolt országokra vonatkoznak."}, "settings": {"editTaxSettings": "Adóbeállítások Szerkesztése", "taxProviderLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "systemTaxProviderLabel": "Ren<PERSON>zer <PERSON>zolgá<PERSON>tó", "calculateTaxesAutomaticallyLabel": "Adók automatikus kiszámítása", "calculateTaxesAutomaticallyHint": "Ha engedélyez<PERSON> van, az adókulcsok automatikusan kiszámításra kerülnek és alkalmazásra kerülnek a kosarakra. <PERSON> le van tilt<PERSON>, az adókat manuálisan kell kiszámítani a fizetés során. A manuális adók ajánlottak harmadik fél adószolgáltatókkal való használatra.", "applyTaxesOnGiftCardsLabel": "Adók alkalmazása ajándékkártyákra", "applyTaxesOnGiftCardsHint": "Ha engedélyezve van, az adók alkalmazásra kerülnek az ajándékkártyákra a fizetés során. Egyes országo<PERSON>ban az adószabályok megkövetelik az adók alkalmazását az ajándékkártyák vásárlásakor.", "defaultTaxRateLabel": "Alapértelmezett adó<PERSON>", "defaultTaxCodeLabel": "Alapértelmezett <PERSON>"}, "defaultRate": {"sectionTitle": "Alapértelmezett <PERSON>"}, "taxRate": {"sectionTitle": "Adókulcsok", "createTaxRate": "Adókulcs Létrehozása", "createTaxRateHint": "Hozzon létre egy új adókulcsot a régióhoz.", "deleteRateDescription": "A(z) {{name}} adókulcs törlésére készül. Ez a művelet nem vonható v<PERSON>.", "editTaxRate": "Adókulcs Szerkesztése", "editRateAction": "Kulcs szerkesztése", "editOverridesAction": "Felülírások szerkesztése", "editOverridesTitle": "Adókulcs Felülírások Szerkesztése", "editOverridesHint": "Adja meg az adókulcs felülírásait.", "deleteTaxRateWarning": "A(z) {{name}} adókulcs törlésére készül. Ez a művelet nem vonható v<PERSON>.", "productOverridesLabel": "Termék felülírások", "productOverridesHint": "Adja meg a termék felülírásokat az adókulcshoz.", "addProductOverridesAction": "Termék felülírások hozzáadása", "productTypeOverridesLabel": "Terméktípus f<PERSON>", "productTypeOverridesHint": "Adja meg a terméktípus felülírásokat az adókulcshoz.", "addProductTypeOverridesAction": "Terméktípus felülírások hozzáadása", "shippingOptionOverridesLabel": "Szállítási opció felülírások", "shippingOptionOverridesHint": "Adja meg a szállítási opció felülírásokat az adókulcshoz.", "addShippingOptionOverridesAction": "Szállítási opció felülírások hozzáadása", "productOverridesHeader": "Termékek", "productTypeOverridesHeader": "Terméktípusok", "shippingOptionOverridesHeader": "Szállítási Opciók"}}, "locations": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editLocation": "<PERSON><PERSON><PERSON><PERSON>", "addSalesChannels": "Értékesítési Csatornák Hozzáadása", "noLocationsFound": "<PERSON><PERSON><PERSON><PERSON>", "selectLocations": "Válassza ki azokat a <PERSON><PERSON><PERSON>, am<PERSON><PERSON> k<PERSON>ten tartják az elemet.", "deleteLocationWarning": "A(z) {{name}} he<PERSON><PERSON><PERSON> törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "removeSalesChannelsWarning_one": "{{count}} értékesítési csatorna eltávolítására készül a helyszínről.", "removeSalesChannelsWarning_other": "{{count}} értékesítési csatornák eltávolítására készül a helyszínről.", "toast": {"create": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON> frissít<PERSON>", "removeChannel": "Értékesítési csatorna si<PERSON>esen eltávolítva"}}, "reservations": {"domain": "Foglalások", "subtitle": "Kezelje a készletelemek foglalt mennyiségét.", "deleteWarning": "<PERSON><PERSON> fog<PERSON>ás törlésére készül. Ez a művelet nem vonható v<PERSON>za."}, "salesChannels": {"domain": "Értékesítési Csatornák", "subtitle": "Kezelje az online és offline csatornákat, amelyeken termékeket értékesít.", "list": {"empty": {"heading": "Nincsenek értékesítési csatornák", "description": "Amint egy értékesítési c<PERSON>orna létrejön, itt fog megjelenni."}, "filtered": {"heading": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> olyan <PERSON><PERSON><PERSON><PERSON>, amely meg<PERSON> a jelenlegi szűrési kritériumoknak."}}, "createSalesChannel": "Értékesítési Csatorna Létrehozása", "createSalesChannelHint": "Hozzon létre egy új értékesítési c<PERSON>orn<PERSON>t, hogy termékeit értékesítse.", "enabledHint": "<PERSON><PERSON> meg, hogy az értékesítési csatorna engedé<PERSON><PERSON><PERSON> van-e.", "removeProductsWarning_one": "{{count}} termék eltávolítására készül a(z) {{sales_channel}} értékesítési csatornáról.", "removeProductsWarning_other": "{{count}} termékek eltávolítására készül a(z) {{sales_channel}} értékesítési csatornáról.", "addProducts": "Termékek Hozzáadása", "editSalesChannel": "Értékesítési Csatorna Szerkesztése", "productAlreadyAdded": "A termék már hozzá lett adva az értékesítési csatornához.", "deleteSalesChannelWarning": "A(z) {{name}} értékesítési csatorna törlésére készül. Ez a művelet nem von<PERSON>ó v<PERSON>za.", "toast": {"create": "Értékesítési c<PERSON>orna si<PERSON>n létrehozva", "update": "Értékesítési csatorna sikeresen frissítve", "delete": "Értékesítési csatorna sikeresen törölve"}, "tooltip": {"cannotDeleteDefault": "<PERSON><PERSON> le<PERSON>t törölni az alapértelmezett értékesítési csatornát"}, "products": {"list": {"noRecordsMessage": "Nincsenek termékek az értékesítési csatornában."}, "add": {"list": {"noRecordsMessage": "Először hozzon létre egy terméket."}}}}, "apiKeyManagement": {"domain": {"publishable": "Publikus API Kulcsok", "secret": "Titkos API Kulcsok"}, "subtitle": {"publishable": "Kezelje az API kulcsokat, amelyeket a kirakatban használnak, hogy korlátozzák a kérések hatókörét meghatározott értékesítési csatornákra.", "secret": "Kezelje az API kul<PERSON>t, amelyeket az adminisztrátorok hitelesítésére használnak az adminisztrációs alkalmazásokban."}, "status": {"active": "Aktív", "revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": {"publishable": "<PERSON><PERSON><PERSON>", "secret": "Titkos"}, "create": {"createPublishableHeader": "Publikus API Kulcs Létrehozása", "createPublishableHint": "Hozzon létre egy új publikus API kulcsot, hogy korlátozza a kérések hatókörét meghatározott értékesítési csatornákra.", "createSecretHeader": "Titkos API Kulcs Létrehozása", "createSecretHint": "Hozzon létre egy új titkos API kulcsot, hogy hozzáférjen a Medusa API-hoz hitelesített adminisztrátorként.", "secretKeyCreatedHeader": "Titkos Kulcs Létrehozva", "secretKeyCreatedHint": "Az új titkos kulcs létrehozva. Másolja és tárolja biztonságosan most. Ez az egyetlen alkalom, amikor megjelenik.", "copySecretTokenSuccess": "A titkos kulcs sikeresen másolva a vágólapra.", "copySecretTokenFailure": "A titkos kulcs másolása a vágólapra sikertelen.", "successToast": "Az API kulcs sikeresen létrehozva."}, "edit": {"header": "API Kulcs Szerkesztése", "description": "Az API kulcs címének szerkesztése.", "successToast": "A(z) {{title}} API kulcs sikeresen frissítve."}, "salesChannels": {"title": "Értékesítési Csatornák Hozzáadása", "description": "Adja hozzá azokat az értékesítési csatornákat, amelyekre az API kulcs korlátozva legyen.", "successToast_one": "{{count}} értékesítési csatorna sikeresen hozzáadva az API kulcshoz.", "successToast_other": "{{count}} értékesítési csatornák sikeresen hozzáadva az API kulcshoz.", "alreadyAddedTooltip": "Az értékesítési csatorna már hozzá lett adva az API kulcshoz.", "list": {"noRecordsMessage": "Nincsenek értékesítési csatornák a publikus API kulcs hatókörében."}}, "delete": {"warning": "A(z) {{title}} API kulcs törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "successToast": "A(z) {{title}} API kulcs sikeresen törölve."}, "revoke": {"warning": "A(z) {{title}} API kulcs visszavonására készül. Ez a művelet nem vonható vissza.", "successToast": "A(z) {{title}} API kulcs sikeresen visszavonva."}, "addSalesChannels": {"list": {"noRecordsMessage": "Először ho<PERSON>n létre egy értékesítési csatornát."}}, "removeSalesChannel": {"warning": "A(z) {{name}} értékesítési csatorna eltávolítására készül az API kulcsról. Ez a művelet nem vonható v<PERSON>za.", "warningBatch_one": "{{count}} értékesítési csatorna eltávolítására készül az API kulcsról. Ez a művelet nem vonható vissza.", "warningBatch_other": "{{count}} értékesítési csatornák eltávolítására készül az API kulcsról. Ez a művelet nem vonható vissza.", "successToast": "Az értékesítési csatorna sikeresen eltávolítva az API kulcsról.", "successToastBatch_one": "{{count}} értékesítési csatorna si<PERSON>esen eltávolítva az API kulcsról.", "successToastBatch_other": "{{count}} értékesítési csatornák sikeresen eltávolítva az API kulcsról."}, "actions": {"revoke": "API kulcs visszavonása", "copy": "API kulcs másolása", "copySuccessToast": "Az API kulcs sikeresen másolva a vágólapra."}, "table": {"lastUsedAtHeader": "<PERSON><PERSON><PERSON><PERSON>", "createdAtHeader": "V<PERSON>zavonva Ekkor"}, "fields": {"lastUsedAtLabel": "<PERSON><PERSON><PERSON><PERSON>", "revokedByLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revokedAtLabel": "Visszavonva ekkor", "createdByLabel": "Létrehozva <PERSON>"}}, "returnReasons": {"domain": "Visszaküld<PERSON><PERSON>", "subtitle": "Kezelje a visszaküldött tételek okait.", "calloutHint": "Kezelje az okokat, hogy kategorizálja a visszaküldéseket.", "editReason": "Visszaküldési Ok Szerkesztése", "create": {"header": "Visszaküldési Ok Hozzáadása", "subtitle": "Adja meg a leggyakoribb okokat a visszaküldésekhez.", "hint": "Hozzon létre egy új visszaküldési okot, hogy kategorizálja a visszaküldéseket.", "successToast": "A(z) {{label}} visszaküldési ok sikeresen létrehozva."}, "edit": {"header": "Visszaküldési Ok Szerkesztése", "subtitle": "A visszaküldési ok értékének szerkesztése.", "successToast": "A(z) {{label}} visszaküldési ok sikeresen frissítve."}, "delete": {"confirmation": "A(z) {{label}} visszaküldési ok törlésére készül. Ez a művelet nem vonható vissza.", "successToast": "A(z) {{label}} visszaküldési ok sikeresen törölve."}, "fields": {"value": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "wrong_size", "tooltip": "Az értéknek egyedi azonosítónak kell lennie a visszaküldési okhoz."}, "label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "description": {"label": "Le<PERSON><PERSON><PERSON>", "placeholder": "A vásárló rossz méretet kapott"}}}, "login": {"forgotPassword": "Elfelejtette a jelszavát? - <0>Visszaállítás</0>", "title": "Üdvözöljük a Medusában", "hint": "Jelentkezzen be az adminisztrációs terület eléréséhez"}, "invite": {"title": "Üdvözöljük a Medusában", "hint": "<PERSON><PERSON> létre fi<PERSON><PERSON><PERSON><PERSON>", "backToLogin": "Vissza a bejelentkezéshez", "createAccount": "Fiók létrehozása", "alreadyHaveAccount": "<PERSON><PERSON><PERSON>? - <0>Jelentkezzen be</0>", "emailTooltip": "Az email cím nem módosítható. Ha másik email címet s<PERSON>, új meghívót kell küldeni.", "invalidInvite": "A meghívó érvénytelen vagy le<PERSON>.", "successTitle": "Fiókja regisztr<PERSON><PERSON><PERSON> lett", "successHint": "Kezdje el azonnal használni a Medusa Admin-t.", "successAction": "<PERSON><PERSON><PERSON> Admin indítás<PERSON>", "invalidTokenTitle": "Meghívó tokenje <PERSON>vénytelen", "invalidTokenHint": "Próbáljon új meghívó linket kérni.", "passwordMismatch": "A jelszavak nem egyeznek", "toast": {"accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>"}}, "resetPassword": {"title": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "hint": "Adja meg email c<PERSON><PERSON><PERSON>, és küldünk Önnek utasításokat a jelszó visszaállításához.", "email": "Email", "sendResetInstructions": "Visszaállítási utasítások küldése", "backToLogin": "<0>Vissza a bejelentkezéshez</0>", "newPasswordHint": "Válasszon új j<PERSON>.", "invalidTokenTitle": "A visszaállítási tokenje érvénytelen", "invalidTokenHint": "Próbáljon új visszaállítási linket kérni.", "expiredTokenTitle": "A visszaállítási tokenje lej<PERSON>rt", "goToResetPassword": "Ugrás a Jelszó Visszaállításához", "resetPassword": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "tokenExpiresIn": "A token lejár <0>{{time}}</0> perc múlva", "successfulRequestTitle": "Sikeresen küldtünk Önnek egy emailt", "successfulRequest": "Küldtünk Önnek egy <PERSON>t, amelyet has<PERSON> a j<PERSON><PERSON> v<PERSON>zaállításához. Ellen<PERSON><PERSON>ze a spam mappát, ha néhány perc múlva nem kapta meg.", "successfulResetTitle": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>zaállítása si<PERSON>", "successfulReset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON><PERSON> be a bejelentkezési oldalon.", "passwordMismatch": "A jelszavak nem egyeznek", "invalidLinkTitle": "A visszaállítási linkje érvénytelen", "invalidLinkHint": "Próbálja újra visszaállítani a jelszavát."}, "workflowExecutions": {"domain": "Munkafolyamatok", "subtitle": "Tekintse meg és kövesse nyomon a munkafolyamat végrehajtásokat a Medusa alkalmazásában.", "transactionIdLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "workflowIdLabel": "Munkafolyamat Azonosító", "progressLabel": "<PERSON><PERSON><PERSON>", "stepsCompletedLabel_one": "{{completed}} a(z) {{count}} lépésből", "stepsCompletedLabel_other": "{{completed}} a(z) {{count}} lépésből", "list": {"noRecordsMessage": "Még nem hajtottak végre munkafolyamatokat."}, "history": {"sectionTitle": "Előzmények", "runningState": "Fut...", "awaitingState": "Várakozik", "failedState": "Sikertelen", "skippedState": "<PERSON><PERSON><PERSON><PERSON>", "skippedFailureState": "<PERSON><PERSON><PERSON><PERSON> (Hiba)", "definitionLabel": "Definíció", "outputLabel": "<PERSON><PERSON><PERSON>", "compensateInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "revertedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorLabel": "Hiba"}, "state": {"done": "<PERSON><PERSON><PERSON>", "failed": "Sikertelen", "reverted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoking": "Meghívás", "compensating": "Kompenzálás", "notStarted": "<PERSON><PERSON>"}, "transaction": {"state": {"waitingToCompensate": "Várakozik a kompenzálásra"}}, "step": {"state": {"skipped": "<PERSON><PERSON><PERSON><PERSON>", "skippedFailure": "<PERSON><PERSON><PERSON><PERSON> (Hiba)", "dormant": "<PERSON><PERSON><PERSON>", "timeout": "Időtúllépés"}}}, "productTypes": {"domain": "Terméktípusok", "subtitle": "Szervezze termékeit típusokba.", "create": {"header": "Terméktípus <PERSON>", "hint": "Hozzon létre egy új terméktípust, hogy kategorizá<PERSON>ja termékeit.", "successToast": "A(z) {{value}} terméktípus sikeresen létrehozva."}, "edit": {"header": "Terméktípus Szerkesztése", "successToast": "A(z) {{value}} terméktípus sikeresen frissítve."}, "delete": {"confirmation": "A(z) {{value}} terméktípus törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "successToast": "A(z) {{value}} terméktípus sikeresen törölve."}, "fields": {"value": "<PERSON><PERSON><PERSON><PERSON>"}}, "productTags": {"domain": "Termékcímkék", "create": {"header": "Termékcímke <PERSON>", "subtitle": "Hozzon létre egy új term<PERSON>, hogy kategorizálja termékeit.", "successToast": "A(z) {{value}} termékcímke si<PERSON>n l<PERSON>z<PERSON>."}, "edit": {"header": "Termékcímke Szerkesztése", "subtitle": "A termékcímke értékének szerkesztése.", "successToast": "A(z) {{value}} termékcímke sikeresen frissítve."}, "delete": {"confirmation": "A(z) {{value}} termékcímke törlésére készül. Ez a művelet nem vonható v<PERSON>za.", "successToast": "A(z) {{value}} termékcímke si<PERSON>esen törö<PERSON>."}, "fields": {"value": "<PERSON><PERSON><PERSON><PERSON>"}}, "notifications": {"domain": "Értesítések", "emptyState": {"title": "Nincsenek értesítések", "description": "<PERSON><PERSON><PERSON> ninc<PERSON>sei, de amint <PERSON>, itt fognak meg<PERSON>ni."}, "accessibility": {"description": "értesítések a Medusa tevékenységeiről itt lesznek felsorolva."}}, "errors": {"serverError": "Szerver hiba - Próbálja újra k<PERSON>őbb.", "invalidCredentials": "Helytelen email vagy j<PERSON>"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktív", "inactive": "Inaktív", "draft": "Piszkozat", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON>"}, "labels": {"productVariant": "Termékvariáns", "prices": "<PERSON><PERSON>", "available": "Elérhető", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removed": "Eltávolítva", "from": "Tól", "to": "Ig", "beaware": "<PERSON><PERSON><PERSON>", "loading": "Betöltés"}, "fields": {"amount": "Összeg", "refundAmount": "Visszatérítési összeg", "name": "Név", "default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "Vezetéknév", "firstName": "Keresztnév", "title": "Cím", "customTitle": "<PERSON>gyed<PERSON> cím", "manageInventory": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "inventoryKit": "<PERSON>", "inventoryItems": "Készletelemek", "inventoryItem": "Készletelem", "requiredQuantity": "Szükséges mennyiség", "description": "Le<PERSON><PERSON><PERSON>", "email": "Email", "password": "Je<PERSON><PERSON><PERSON>", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>ó megerősítése", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "categories": "Kate<PERSON><PERSON><PERSON><PERSON>", "shippingMethod": "Szállítási mód", "configurations": "Konfigurációk", "conditions": "Feltételek", "category": "Kategória", "collection": "Gyűjtemény", "discountable": "Kedvezményezhető", "handle": "Fogantyú", "subtitle": "Alcím", "by": "<PERSON><PERSON><PERSON>", "item": "Tétel", "qty": "menny.", "limit": "Limit", "tags": "Címkék", "type": "<PERSON><PERSON><PERSON>", "reason": "Ok", "none": "nincs", "all": "összes", "search": "Keresés", "percentage": "Százalék", "sales_channels": "Értékesítési Csatornák", "customer_groups": "Vásárlói Csoportok", "product_tags": "Termékcímkék", "product_types": "Terméktípusok", "product_collections": "Termékgyűjtemények", "status": "<PERSON><PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON>", "dynamic": "<PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON><PERSON>", "years": "Évek", "months": "Hónapok", "days": "Napok", "hours": "<PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "totalRedemptions": "Összes Beváltás", "countries": "Országok", "paymentProviders": "Fizetési S<PERSON>gá<PERSON>ók", "refundReason": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fulfillmentProviders": "Teljesítési Szolgáltatók", "fulfillmentProvider": "Teljesítési Szolgáltató", "providers": "Szolgáltatók", "availability": "Elérhetőség", "inventory": "<PERSON><PERSON><PERSON><PERSON>", "optional": "Opcionális", "note": "Megjegyzés", "automaticTaxes": "Automat<PERSON><PERSON>", "taxInclusivePricing": "<PERSON><PERSON><PERSON>zámított <PERSON>", "currency": "Pénznem", "address": "Cím", "address2": "Lakás, lakosztály, stb.", "city": "<PERSON><PERSON><PERSON>", "postalCode": "Irányí<PERSON>", "country": "<PERSON><PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "province": "Tartomány", "company": "Cég", "phone": "Telefon", "metadata": "Metaadatok", "selectCountry": "Ország kiválasztása", "products": "Termékek", "variants": "Variánsok", "orders": "<PERSON><PERSON><PERSON><PERSON>", "account": "<PERSON>ók", "total": "<PERSON><PERSON><PERSON>", "paidTotal": "Összesen beszedve", "totalExclTax": "Összesen ad<PERSON>", "subtotal": "Részösszeg", "shipping": "Szállítás", "outboundShipping": "<PERSON><PERSON><PERSON>llí<PERSON>", "returnShipping": "Visszaküldési Szállítás", "tax": "<PERSON><PERSON>", "created": "Létrehozva", "key": "<PERSON><PERSON><PERSON>", "customer": "Vásárló", "date": "<PERSON><PERSON><PERSON>", "order": "Rendelés", "fulfillment": "Teljesítés", "provider": "S<PERSON>lgáltató", "payment": "<PERSON><PERSON><PERSON><PERSON>", "items": "Tételek", "salesChannel": "Értékesítési <PERSON>", "region": "<PERSON><PERSON><PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Szerep", "sent": "Elküldve", "salesChannels": "Értékesítési Csatornák", "product": "Termék", "createdAt": "Létrehozva", "updatedAt": "Frissítve", "revokedAt": "Visszavonva ekkor", "true": "Igaz", "false": "<PERSON><PERSON>", "giftCard": "<PERSON>j<PERSON><PERSON>ékk<PERSON><PERSON><PERSON>", "tag": "<PERSON><PERSON><PERSON><PERSON>", "dateIssued": "Kibocsátás <PERSON>", "issuedDate": "Kibocsátás <PERSON>", "expiryDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "priceTemplate": "Ár {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width": "Szélesség", "length": "Hossz", "weight": "Súly", "midCode": "MID kód", "hsCode": "HS kód", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "<PERSON><PERSON><PERSON>t mennyisége", "barcode": "<PERSON><PERSON><PERSON><PERSON>", "countryOfOrigin": "Származási ország", "material": "<PERSON><PERSON>", "thumbnail": "Előnézeti kép", "sku": "SKU", "managedInventory": "<PERSON><PERSON><PERSON>", "allowBackorder": "Visszarendelés engedélyezése", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "Mennyiség", "variant": "<PERSON><PERSON><PERSON><PERSON>", "id": "Azonosító", "parent": "Szülő", "minSubtotal": "<PERSON><PERSON>", "maxSubtotal": "Max. <PERSON>zeg", "shippingProfile": "Szállítási Profil", "summary": "Összegzés", "details": "Részletek", "label": "<PERSON><PERSON><PERSON><PERSON>", "rate": "<PERSON><PERSON><PERSON>", "requiresShipping": "Szállítást igényel", "unitPrice": "<PERSON>gy<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endDate": "Befejezési d<PERSON>", "draft": "Piszkozat", "values": "<PERSON>rté<PERSON><PERSON>"}, "quotes": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Vásárlói árajánlatok és javaslatok kezelése", "noQuotes": "<PERSON><PERSON>", "noQuotesDescription": "Jelenleg nincsenek árajánlatok. Hozzon létre egyet az üzletből.", "table": {"id": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Vásárló", "status": "<PERSON><PERSON><PERSON><PERSON>", "company": "Cég", "amount": "Összeg", "createdAt": "Létrehozva", "updatedAt": "Frissítve", "actions": "Műveletek"}, "status": {"pending_merchant": "Kereskedő várakozik", "pending_customer": "Vásárló v<PERSON>rak<PERSON>", "merchant_rejected": "Kereskedő elutasította", "customer_rejected": "Vás<PERSON><PERSON><PERSON>", "accepted": "Elfogadva", "unknown": "Ismeretlen"}, "actions": {"sendQuote": "Árajánlat k<PERSON>", "rejectQuote": "Árajánlat elutasí<PERSON>ás<PERSON>", "viewOrder": "Rendelés megtekintése"}, "details": {"header": "<PERSON><PERSON><PERSON><PERSON>", "quoteSummary": "Árajánlat összegzése", "customer": "Vásárló", "company": "Cég", "items": "Tételek", "total": "Összesen", "subtotal": "Részösszeg", "shipping": "Szállítás", "tax": "<PERSON><PERSON>", "discounts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "originalTotal": "Eredeti összeg", "quoteTotal": "Árajánlat összege", "messages": "Üzenetek", "actions": "Műveletek", "sendMessage": "Üzenet küldése", "send": "<PERSON><PERSON><PERSON><PERSON>", "pickQuoteItem": "Árajánlat tétel kiválasztása", "selectQuoteItem": "Válasszon egy <PERSON>lat tételt a megjegyzéshez", "selectItem": "Tétel kiválasztása", "manage": "Kezelés", "phone": "Telefon", "spendingLimit": "Költési limit", "name": "Név", "manageQuote": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "noItems": "Nincsenek tételek ebben a<PERSON>", "noMessages": "Nincsenek üzenetek ehhez az árajánlathoz"}, "items": {"title": "Termék", "quantity": "Mennyiség", "unitPrice": "<PERSON>gy<PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "Összesen"}, "messages": {"admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customer": "Vásárló", "placeholder": "<PERSON><PERSON><PERSON> be üzenetét ide..."}, "filters": {"status": "Szűrés st<PERSON><PERSON> s<PERSON>"}, "confirmations": {"sendTitle": "Árajánlat k<PERSON>", "sendDescription": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy el szeretné küldeni ezt az árajánlatot a vásárlónak?", "rejectTitle": "Árajánlat elutasí<PERSON>ás<PERSON>", "rejectDescription": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy el szeretné utasítani ezt az árajánlatot?"}, "acceptance": {"message": "<PERSON><PERSON> elfogadásra k<PERSON>ü<PERSON>"}, "toasts": {"sendSuccess": "Árajánlat si<PERSON>esen elküldve a vásárlónak", "sendError": "Árajánlat küldése si<PERSON>en", "rejectSuccess": "Vásárlói <PERSON> si<PERSON>esen elutasítva", "rejectError": "Árajánlat elutasí<PERSON><PERSON>", "messageSuccess": "Üzenet sikeresen elküldve a vásárlónak", "messageError": "Üzenet küldése sikertelen", "updateSuccess": "Áraj<PERSON>lat si<PERSON>esen frissítve"}, "manage": {"overridePriceHint": "<PERSON>z eredeti ár felülírása ennél a tételnél", "updatePrice": "<PERSON><PERSON> <PERSON>"}}, "companies": {"domain": "Cégek", "title": "Cégek", "subtitle": "Üzleti kapcsolatok kezelése", "noCompanies": "<PERSON><PERSON> cégek", "noCompaniesDescription": "Hozza létre első cégét a kezdéshez.", "notFound": "Cég nem tal<PERSON>", "table": {"name": "Név", "phone": "Telefon", "email": "Email", "address": "Cím", "employees": "Alkalmazottak", "customerGroup": "Vásárlói csoport", "actions": "Műveletek"}, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "Email", "phone": "Telefon", "website": "Weboldal", "address": "Cím", "city": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "zip": "Irányí<PERSON>", "zipCode": "Irányí<PERSON>", "country": "<PERSON><PERSON><PERSON><PERSON>", "currency": "Pénznem", "logoUrl": "Logó URL", "description": "Le<PERSON><PERSON><PERSON>", "employees": "Alkalmazottak", "customerGroup": "Vásárlói csoport", "approvalSettings": "Jóváhagyási beállítások"}, "placeholders": {"name": "Adja meg a cégnevet", "email": "Adja meg az email címet", "phone": "Adja meg a telefonszámot", "website": "Adja meg a weboldal URL-jét", "address": "Adja meg a címet", "city": "Adja meg a vá<PERSON>t", "state": "<PERSON>ja meg az <PERSON>", "zip": "Adja meg az irányítószámot", "logoUrl": "Adja meg a logó URL-jét", "description": "Adja meg a cég leí<PERSON>", "selectCountry": "<PERSON><PERSON><PERSON><PERSON>", "selectCurrency": "Válasszon pénznemet"}, "validation": {"nameRequired": "A cégnév kötelező", "emailRequired": "Az email kötelező", "emailInvalid": "Érvénytelen email cím", "addressRequired": "A cím kötelező", "cityRequired": "A város kötelező", "stateRequired": "<PERSON>z állam kötelező", "zipRequired": "Az irányítószám kö<PERSON>ező"}, "create": {"title": "Cég lé<PERSON>ho<PERSON>", "description": "Hozzon létre egy új céget az üzleti kapcsolatok kezeléséhez.", "submit": "Cég lé<PERSON>ho<PERSON>"}, "edit": {"title": "<PERSON><PERSON>g s<PERSON>té<PERSON>", "submit": "<PERSON><PERSON><PERSON>"}, "details": {"actions": "Műveletek"}, "approvals": {"requiresAdminApproval": "Adminisztrátori jóváhagyás szükséges", "requiresSalesManagerApproval": "Értékesítési vezető jóváhagyása szükséges", "noApprovalRequired": "<PERSON><PERSON><PERSON> s<PERSON>ükség jóváhagyásra"}, "deleteWarning": "Ez véglegesen törli a céget és az összes kapcsolódó adatot.", "approvalSettings": {"title": "Jóváhagyási beállítások", "requiresAdminApproval": "Adminisztrátori jóváhagyás szükséges", "requiresSalesManagerApproval": "Értékesítési vezető jóváhagyása szükséges", "requiresAdminApprovalDesc": "Az ettől a cégtől érkező rendelések feldolgozás előtt adminisztrátori jóváhagyást igényelnek", "requiresSalesManagerApprovalDesc": "Az ettől a cégtől érkező rendelések feldolgozás előtt értékesítési vezető jóváhagyását igénylik", "updateSuccess": "Jóváhagyási beállítások sikeresen frissítve", "updateError": "Jóváhagyási beállítások frissítése sikertelen"}, "customerGroup": {"title": "Vásárlói csoport kezelése", "hint": "Rendelje hozzá ezt a céget egy vásárlói csoporthoz a csoportspecifikus árak és engedélyek alkalmazásához.", "name": "Vásárlói csoport neve", "groupName": "Vásárlói csoport", "actions": "Műveletek", "add": "Hozzáadás", "remove": "Eltávolítás", "description": "Vásárlói csoportok kezelése ehhez a céghez", "noGroups": "Nincsenek elérhető vásárlói csoportok", "addSuccess": "Cég si<PERSON>esen hozzáadva a vásárlói csoporthoz", "addError": "Cég hozzáadása a vásárlói csoporthoz si<PERSON>telen", "removeSuccess": "Cég si<PERSON>esen eltávolítva a vásárlói csoportból", "removeError": "Cég eltávolítása a vásárlói csoportból si<PERSON>telen"}, "actions": {"edit": "<PERSON><PERSON>g s<PERSON>té<PERSON>", "editDetails": "Részletek szerkesztése", "manageCustomerGroup": "Vásárlói csoport kezelése", "approvalSettings": "Jóváhagyási beállítások", "delete": "<PERSON><PERSON><PERSON>", "confirmDelete": "Törlés megerősítése"}, "delete": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy tö<PERSON>ölni szeretné ezt a céget? Ez a művelet nem von<PERSON>ó vissza."}, "employees": {"title": "Alkalmazottak", "noEmployees": "Nem találhatók alkalmazottak ehhez a céghez", "name": "Név", "email": "Email", "phone": "Telefon", "role": "Szerepkör", "spendingLimit": "Költési limit", "admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "employee": "Alkalmazott", "add": "Alkalmazott hozzáadása", "create": {"title": "Alkalmazott létrehozása", "success": "Alkalmazott sikeresen létrehozva", "error": "Alkalmazott létrehozása sikertelen"}, "form": {"details": "Részletes információk", "permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstName": "Keresztnév", "lastName": "Vezetéknév", "email": "Email", "phone": "Telefon", "spendingLimit": "Költési limit", "adminAccess": "Adminisztrátori ho<PERSON>fé<PERSON>s", "isAdmin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAdminDesc": "Adminisztrátori jogosultságok megadása ennek az alkalmazottnak", "isAdminTooltip": "Az adminisztrátorok kezelhetik a cég beállításait és más alkalmazottakat", "firstNamePlaceholder": "Adja meg a keresztnevet", "lastNamePlaceholder": "Adja meg a vezetéknevet", "emailPlaceholder": "Adja meg az email címet", "phonePlaceholder": "Adja meg a telefonszámot", "spendingLimitPlaceholder": "Adja meg a költési limitet", "save": "Men<PERSON>s", "saving": "Mentés..."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy törölni szeretné ezt az alkalmazottat?", "success": "Alkalmazott sikeresen törölve"}, "edit": {"title": "Alkalmazott szerkesztése"}, "toasts": {"updateSuccess": "Alkalmazott sikeresen frissítve", "updateError": "Alkalmazott frissí<PERSON> si<PERSON>en"}}, "toasts": {"createSuccess": "<PERSON><PERSON><PERSON>", "createError": "Cég l<PERSON><PERSON>", "updateSuccess": "<PERSON><PERSON><PERSON> si<PERSON>esen fris<PERSON>ve", "updateError": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> si<PERSON>esen tö<PERSON>", "deleteError": "<PERSON><PERSON>g törl<PERSON>"}}, "approvals": {"domain": "Jóváhagyások", "title": "Jóváhagyások", "subtitle": "Jóváhagyási munkafolyamatok kezelése", "noApprovals": "<PERSON><PERSON>hatók jóváhagyások", "noApprovalsDescription": "<PERSON><PERSON><PERSON> nincsenek jóváhagyások áttekintésre.", "table": {"id": "ID", "type": "<PERSON><PERSON><PERSON>", "company": "Cég", "customer": "Vásárló", "amount": "Összeg", "status": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Létrehozva"}, "status": {"pending": "Függőben", "approved": "Jóváhagyva", "rejected": "Elutasítva", "expired": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Ismeretlen"}, "details": {"header": "Jóváhagyás részletei", "summary": "Jóváhagyás összefoglalója", "company": "Cég", "customer": "Vásárló", "order": "Rendelés", "amount": "Összeg", "updatedAt": "Frissítve", "reason": "Indok", "actions": "Műveletek"}, "actions": {"approve": "Jóváhagyás", "reject": "Elutasítás", "confirmApprove": "Jóváhagyás megerősítése", "confirmReject": "Elutasítás megerősítése", "reasonPlaceholder": "Adja meg az indokot (opcionális)..."}, "filters": {"status": "Szűrés st<PERSON><PERSON> s<PERSON>"}, "toasts": {"approveSuccess": "Sikeresen jóváhagyva", "approveError": "Jóváhagy<PERSON> si<PERSON>", "rejectSuccess": "<PERSON><PERSON><PERSON><PERSON>", "rejectError": "Eluta<PERSON><PERSON><PERSON><PERSON>"}}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "Évek", "months_one": "Hónap", "months_other": "Hónapok", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "Hetek", "days_one": "Nap", "days_other": "Napok", "hours_one": "<PERSON><PERSON>", "hours_other": "<PERSON><PERSON><PERSON>", "minutes_one": "Perc", "minutes_other": "<PERSON><PERSON><PERSON>", "seconds_one": "Másodperc", "seconds_other": "Másodpercek"}}