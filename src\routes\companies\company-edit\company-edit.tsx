import { Container, Heading } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { useParams, useLoaderData } from "react-router-dom"
import { SingleColumnPage } from "../../../components/layout/pages"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { useExtension } from "../../../providers/extension-provider"
import { useCompany } from "../../../hooks/api/companies"
import { AdminCompanyResponse } from "../../../types"
import { CompanyEditForm } from "./components/company-edit-form"
import { companyLoader } from "../company-detail/loader"

export const CompanyEdit = () => {
  const { id } = useParams()
  const { t } = useTranslation()
  const { getWidgets } = useExtension()

  const initialData = useLoaderData() as Awaited<ReturnType<typeof companyLoader>>
  const { company, isLoading, isError, error } = useCompany(id!, undefined, { 
    initialData 
  })

  if (isLoading || !company) {
    return <SingleColumnPageSkeleton sections={1} />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("company.edit.after"),
        before: getWidgets("company.edit.before"),
      }}
    >
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 pb-0">
          <Heading className="font-sans font-medium h1-core">
            {t("companies.edit.title", "Edit Company")}
          </Heading>
        </div>
        <CompanyEditForm company={company} />
      </Container>
    </SingleColumnPage>
  )
}