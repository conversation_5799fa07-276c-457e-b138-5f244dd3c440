import { SingleColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { QuotesTable } from "./components/quotes-table"

export const QuoteList = () => {
  const { getWidgets } = useExtension()

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("quote.list.after"),
        before: getWidgets("quote.list.before"),
      }}
      hasOutlet={false}
    >
      <QuotesTable />
    </SingleColumnPage>
  )
}