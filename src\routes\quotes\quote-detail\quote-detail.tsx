import { CheckCircleSolid } from "@medusajs/icons";
import {
  But<PERSON>,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  usePrompt,
} from "@medusajs/ui";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useNavigate, useParams, Outlet } from "react-router-dom";
import { JsonViewSection } from "../../../components/common/json-view-section";
import { useOrderPreview } from "../../../hooks/api";
import {
  useQuote,
  useRejectQuote,
  useSendQuote,
} from "../../../hooks/api/quotes";
import { getStylizedAmount } from "../../../lib/money-amount-helpers";
import {
  QuoteDetailsHeader,
  QuoteItems,
  CostBreakdown,
  QuoteTotal,
  QuoteMessages,
} from "./components";

export const QuoteDetail = () => {
  const { id: quoteId } = useParams();
  const [showSendQuote, setShowSendQuote] = useState(false);
  const [showRejectQuote, setShowRejectQuote] = useState(false);
  const prompt = usePrompt();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { quote, isLoading } = useQuote(quoteId!, {
    fields: "*draft_order.customer,*draft_order.customer.employee,*draft_order.customer.employee.company",
  });

  const { order: preview, isLoading: isPreviewLoading } = useOrderPreview(
    quote?.draft_order_id!,
    {},
    { enabled: !!quote?.draft_order_id }
  );

  const { mutateAsync: sendQuote, isPending: isSendingQuote } = useSendQuote(
    quoteId!
  );

  const { mutateAsync: rejectQuote, isPending: isRejectingQuote } =
    useRejectQuote(quoteId!);

  useEffect(() => {
    if (["pending_merchant", "customer_rejected"].includes(quote?.status!)) {
      setShowSendQuote(true);
    } else {
      setShowSendQuote(false);
    }

    if (
      ["customer_rejected", "merchant_rejected", "accepted"].includes(
        quote?.status!
      )
    ) {
      setShowRejectQuote(false);
    } else {
      setShowRejectQuote(true);
    }
  }, [quote]);

  const handleSendQuote = async () => {
    const res = await prompt({
      title: t("quotes.confirmations.sendTitle", "Send Quote"),
      description: t("quotes.confirmations.sendDescription", "Are you sure you want to send this quote to the customer?"),
      confirmText: t("actions.continue", "Continue"),
      cancelText: t("actions.cancel", "Cancel"),
      variant: "confirmation",
    });

    if (res) {
      await sendQuote(
        {},
        {
          onSuccess: () => toast.success(t("quotes.toasts.sendSuccess", "Quote sent successfully")),
          onError: (e) => toast.error(e.message),
        }
      );
    }
  };

  const handleRejectQuote = async () => {
    const res = await prompt({
      title: t("quotes.confirmations.rejectTitle", "Reject Quote"),
      description: t("quotes.confirmations.rejectDescription", "Are you sure you want to reject this quote?"),
      confirmText: t("actions.continue", "Continue"),
      cancelText: t("actions.cancel", "Cancel"),
      variant: "confirmation",
    });

    if (res) {
      await rejectQuote(void 0, {
        onSuccess: () =>
          toast.success(t("quotes.toasts.rejectSuccess", "Quote rejected successfully")),
        onError: (e) => toast.error(e.message),
      });
    }
  };

  if (isLoading || !quote) {
    return <></>;
  }

  if (isPreviewLoading) {
    return <></>;
  }

  if (!isPreviewLoading && !preview) {
    throw "preview not found";
  }

  return (
    <div className="flex flex-col gap-y-3">
      <div className="flex flex-col gap-x-4 lg:flex-row xl:items-start">
        <div className="flex w-full flex-col gap-y-3">
          {quote.status === "accepted" && (
            <Container className="divide-y divide-dashed p-0">
              <div className="flex items-center justify-between px-6 py-4">
                <Text className="txt-compact-small">
                  <CheckCircleSolid className="inline-block mr-2 text-green-500 text-lg" />
                  {t("quotes.acceptance.message", "Quote has been accepted")}
                </Text>

                <Button
                  size="small"
                  onClick={() => navigate(`/orders/${quote.draft_order_id}`)}
                >
                  {t("quotes.actions.viewOrder", "View Order")}
                </Button>
              </div>
            </Container>
          )}

          <Container className="divide-y divide-dashed p-0">
            <QuoteDetailsHeader quote={quote} />
            <QuoteItems order={quote.draft_order} preview={preview!} />
            <CostBreakdown order={quote.draft_order} />
            <QuoteTotal order={quote.draft_order} preview={preview!} />

            {(showRejectQuote || showSendQuote) && (
              <div className="bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4">
                {showRejectQuote && (
                  <Button
                    size="small"
                    variant="secondary"
                    onClick={() => handleRejectQuote()}
                    disabled={isSendingQuote}
                  >
                    {t("quotes.actions.rejectQuote", "Reject Quote")}
                  </Button>
                )}

                {showSendQuote && (
                  <Button
                    size="small"
                    variant="secondary"
                    onClick={() => handleSendQuote()}
                    disabled={isSendingQuote}
                  >
                    {t("quotes.actions.sendQuote", "Send Quote")}
                  </Button>
                )}
              </div>
            )}
          </Container>

          <QuoteMessages quote={quote} preview={preview!} />

          <JsonViewSection data={quote} />
        </div>

        <div className="mt-2 flex w-full max-w-[100%] flex-col gap-y-3 xl:mt-0 xl:max-w-[400px]">
          <Container className="divide-y p-0">
            <div className="flex items-center justify-between px-6 py-4">
              <Heading level="h2">{t("quotes.details.customer", "Customer")}</Heading>
            </div>

            <div className="text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4">
              <Text size="small" weight="plus" leading="compact">
                {t("fields.email")}
              </Text>

              <Link
                className="text-sm text-pretty text-blue-500"
                to={`/customers/${quote.customer?.id}`}
                onClick={(e) => e.stopPropagation()}
              >
                {quote.customer?.email}
              </Link>
            </div>

            <div className="text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4">
              <Text size="small" weight="plus" leading="compact">
                {t("quotes.details.phone", "Phone")}
              </Text>

              <Text size="small" leading="compact" className="text-pretty">
                {quote.customer?.phone || "-"}
              </Text>
            </div>

            <div className="text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4">
              <Text size="small" weight="plus" leading="compact">
                {t("quotes.details.spendingLimit", "Spending Limit")}
              </Text>

              <Text size="small" leading="compact" className="text-pretty">
                {quote.draft_order?.customer?.employee?.spending_limit && quote.draft_order?.customer?.employee?.company?.currency_code
                  ? getStylizedAmount(
                      quote.draft_order.customer.employee.spending_limit,
                      quote.draft_order.customer.employee.company.currency_code
                    )
                  : "-"}
              </Text>
            </div>
          </Container>

          <Container className="divide-y p-0">
            <div className="flex items-center justify-between px-6 py-4">
              <Heading level="h2">{t("quotes.details.company", "Company")}</Heading>
            </div>

            <div className="text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4">
              <Text size="small" weight="plus" leading="compact">
                {t("quotes.details.name", "Name")}
              </Text>

              <Link
                className="text-sm text-pretty text-blue-500"
                to={`/companies/${quote.draft_order?.customer?.employee?.company?.id}`}
                onClick={(e) => e.stopPropagation()}
              >
                {quote.draft_order?.customer?.employee?.company?.name || quote.customer?.company_name || "-"}
              </Link>
            </div>
          </Container>
        </div>
      </div>

      <Toaster />
      <Outlet />
    </div>
  );
};