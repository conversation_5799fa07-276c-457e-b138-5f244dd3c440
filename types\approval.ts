// 审批状态枚举
export enum ApprovalStatusType {
  PENDING = "pending",
  APPROVED = "approved", 
  REJECTED = "rejected",
  EXPIRED = "expired"
}

// 审批类型枚举
export enum ApprovalType {
  ADMIN = "admin",
  SALES_MANAGER = "sales_manager"
}

// 审批接口
export interface Approval {
  id: string
  company_id: string
  order_id?: string
  customer_id: string
  status: ApprovalStatusType
  type: ApprovalType
  entity_id: string
  amount?: number
  currency_code?: string
  reason?: string
  approved_by?: string
  rejected_by?: string
  created_at: string
  updated_at: string
  company?: {
    id: string
    name: string
  }
  customer?: {
    id: string
    email: string
    first_name: string
    last_name: string
  }
  order?: {
    id: string
    display_id: number
    total: number
    currency_code: string
  }
}
