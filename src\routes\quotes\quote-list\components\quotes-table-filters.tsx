import { Select } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Quote } from "../../../../types"

interface QuotesTableFiltersProps {
  status?: Quote["status"]
  onStatusChange: (status?: Quote["status"]) => void
}

export const QuotesTableFilters = ({
  status,
  onStatusChange,
}: QuotesTableFiltersProps) => {
  const { t } = useTranslation()

  const statusOptions = [
    { value: "all", label: t("general.all", "All") },
    { 
      value: "pending_merchant", 
      label: t("quotes.status.pending_merchant", "Pending Merchant") 
    },
    { 
      value: "pending_customer", 
      label: t("quotes.status.pending_customer", "Pending Customer") 
    },
    { 
      value: "merchant_rejected", 
      label: t("quotes.status.merchant_rejected", "Merchant Rejected") 
    },
    { 
      value: "customer_rejected", 
      label: t("quotes.status.customer_rejected", "Customer Rejected") 
    },
    { 
      value: "accepted", 
      label: t("quotes.status.accepted", "Accepted") 
    },
  ]

  return (
    <div className="flex items-center gap-2 p-4">
      <Select
        value={status || "all"}
        onValueChange={(value) => 
          onStatusChange(value === "all" ? undefined : value as Quote["status"])
        }
      >
        <Select.Trigger className="w-48">
          <Select.Value placeholder={t("quotes.filters.status", "Filter by status")} />
        </Select.Trigger>
        <Select.Content>
          {statusOptions.map((option) => (
            <Select.Item key={option.value} value={option.value}>
              {String(option.label)}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  )
}