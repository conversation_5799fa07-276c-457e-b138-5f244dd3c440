import { But<PERSON>, Drawer, Hint, Table, toast } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { useAddCompanyToCustomerGroup, useRemoveCompanyFromCustomerGroup } from "../../../../../hooks/api/companies"
import { Company } from "../../../../../types"
import { HttpTypes } from "@medusajs/types"

interface CompanyCustomerGroupDrawerProps {
  company: Company
  customerGroups?: HttpTypes.AdminCustomerGroup[]
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyCustomerGroupDrawer = ({ 
  company, 
  customerGroups, 
  open, 
  setOpen 
}: CompanyCustomerGroupDrawerProps) => {
  const { t } = useTranslation()
  
  const { mutateAsync: addMutate, isPending: addLoading } = useAddCompanyToCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success(t("companies.customerGroup.addSuccess", "Successfully added company to customer group"))
      setOpen(false)
    },
    onError: () => {
      toast.error(t("companies.customerGroup.addError", "Failed to add company to customer group"))
    },
  })

  const { mutateAsync: removeMutate, isPending: removeLoading } = useRemoveCompanyFromCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success(t("companies.customerGroup.removeSuccess", "Successfully removed company from customer group"))
    },
    onError: () => {
      toast.error(t("companies.customerGroup.removeError", "Failed to remove company from customer group"))
    },
  })

  const handleAdd = async (groupId: string) => {
    await addMutate(groupId)
  }

  const handleRemove = async (groupId: string) => {
    await removeMutate(groupId)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>{t("companies.actions.manageCustomerGroup", "Manage Customer Group")}</Drawer.Title>
        </Drawer.Header>

        <Drawer.Body className="space-y-4 h-full overflow-y-hidden">
          <Hint variant="info">
            {t("companies.customerGroup.description", `Manage customer groups for company "${company.name}". This company has ${company.employees?.length || 0} employees.`)}
          </Hint>

          <div className="overflow-y-auto">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>{t("companies.customerGroup.name", "Customer Group Name")}</Table.HeaderCell>
                  <Table.HeaderCell className="text-right">{t("general.actions", "Actions")}</Table.HeaderCell>
                </Table.Row>
              </Table.Header>

              <Table.Body>
                {customerGroups ? (
                  customerGroups.map((group) => (
                    <Table.Row key={group.id}>
                      <Table.Cell>{group.name}</Table.Cell>
                      <Table.Cell className="text-right">
                        {company.customer_group?.id &&
                        company.customer_group.id === group.id ? (
                          <Button
                            onClick={() => handleRemove(group.id)}
                            isLoading={removeLoading}
                            variant="danger"
                            size="small"
                          >
                            {t("actions.remove", "Remove")}
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleAdd(group.id)}
                            disabled={
                              (company.customer_group?.id &&
                                company.customer_group.id !== group.id) ||
                              addLoading
                            }
                            isLoading={addLoading}
                            size="small"
                          >
                            {t("actions.add", "Add")}
                          </Button>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : (
                  <Table.Row>
                    <Table.Cell colSpan={2} className="text-center">
                      {t("companies.customerGroup.noGroups", "No customer groups available")}
                    </Table.Cell>
                  </Table.Row>
                )}
              </Table.Body>
            </Table>
          </div>
        </Drawer.Body>
      </Drawer.Content>
    </Drawer>
  )
}
