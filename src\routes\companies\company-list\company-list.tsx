import { SingleColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { CompaniesTable } from "./components"

export const CompanyList = () => {
  const { getWidgets } = useExtension()

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("company.list.after"),
        before: getWidgets("company.list.before"),
      }}
    >
      <CompaniesTable />
    </SingleColumnPage>
  )
}