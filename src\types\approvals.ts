export interface Approval {
  id: string
  company_id: string
  order_id?: string
  customer_id: string
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  type: string
  entity_id: string
  amount?: number
  currency_code?: string
  reason?: string
  approved_by?: string
  rejected_by?: string
  created_at: string
  updated_at: string
  company?: {
    id: string
    name: string
  }
  customer?: {
    id: string
    email: string
    first_name: string
    last_name: string
  }
  order?: {
    id: string
    display_id: number
    total: number
    currency_code: string
  }
}

export interface AdminApproval {
  id: string
  company_id: string
  status: Approval['status']
  type: string
  entity_id: string
  created_at: string
  updated_at: string
}

export interface AdminApprovalsResponse {
  approvals: Approval[]
  count: number
  offset: number
  limit: number
}

export interface AdminUpdateApproval {
  status: 'approved' | 'rejected'
  reason?: string
}

export interface AdminUpdateApprovalSettings {
  requires_admin_approval?: boolean
  requires_sales_manager_approval?: boolean
}

export interface AdminApprovalSettings {
  id: string
  company_id: string
  requires_admin_approval: boolean
  requires_sales_manager_approval: boolean
  created_at: string
  updated_at: string
}