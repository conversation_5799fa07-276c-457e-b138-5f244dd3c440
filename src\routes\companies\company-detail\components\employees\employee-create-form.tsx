import {
  Button,
  CurrencyInput,
  Drawer,
  Input,
  Label,
  Switch,
  Text,
  Tooltip,
  Container,
} from "@medusajs/ui";
import { InformationCircleSolid } from "@medusajs/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Company } from "../../../../../types";
import { getCurrencySymbol } from "../../../../../lib/data/currencies";

// CoolSwitch component - matches old version styling
interface CoolSwitchProps {
  fieldName: string;
  label: string;
  description: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  tooltip?: string;
}

const CoolSwitch = ({ fieldName, label, description, checked, onChange, tooltip }: CoolSwitchProps) => {
  return (
    <Container className="bg-ui-bg-subtle flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <Switch name={fieldName} checked={checked} onCheckedChange={onChange} />
        <Label size="xsmall" className="txt-compact-small font-medium">
          {label}
        </Label>
        {tooltip && (
          <Tooltip content={tooltip} className="z-50">
            <InformationCircleSolid color="gray" />
          </Tooltip>
        )}
      </div>
      <Text size="xsmall">{description}</Text>
    </Container>
  );
};

export function EmployeeCreateForm({
  handleSubmit,
  loading,
  error,
  company,
}: {
  handleSubmit: (data: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    is_admin: boolean;
    spending_limit: number;
  }) => Promise<void>;
  loading: boolean;
  error: Error | null;
  company: Company;
}) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<{
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    is_admin: boolean;
    spending_limit: string;
  }>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    is_admin: false,
    spending_limit: "0",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value =
      e.target.type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : e.target.value;

    setFormData({ ...formData, [e.target.name]: value });
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const spendingLimit = formData.spending_limit
      ? parseInt(formData.spending_limit)
      : 0;

    const data = {
      ...formData,
      spending_limit: spendingLimit,
    };

    handleSubmit(data);
  };

  return (
    <form onSubmit={onSubmit}>
      <Drawer.Body className="flex flex-col p-4 gap-6">
        <div className="flex flex-col gap-3">
          <h2 className="h2-core">{t("companies.employees.form.details")}</h2>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.firstName")}
            </Label>
            <Input
              type="text"
              name="first_name"
              onChange={handleChange}
              placeholder={t("companies.employees.form.firstNamePlaceholder")}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.lastName")}
            </Label>
            <Input
              type="text"
              name="last_name"
              onChange={handleChange}
              placeholder={t("companies.employees.form.lastNamePlaceholder")}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.email")}
            </Label>
            <Input
              type="email"
              name="email"
              onChange={handleChange}
              placeholder={t("companies.employees.form.emailPlaceholder")}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.phone")}
            </Label>
            <Input
              type="text"
              name="phone"
              onChange={handleChange}
              placeholder={t("companies.employees.form.phonePlaceholder")}
            />
          </div>
        </div>
        <div className="flex flex-col gap-3">
          <h2 className="h2-core">{t("companies.employees.form.permissions")}</h2>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.spendingLimit")} ({company.currency_code?.toUpperCase() || "USD"})
            </Label>
            <CurrencyInput
              symbol={getCurrencySymbol(company.currency_code)}
              code={company.currency_code || "USD"}
              type="text"
              name="spending_limit"
              value={formData.spending_limit ? formData.spending_limit : ""}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  spending_limit: e.target.value.replace(/[^0-9]/g, ""),
                })
              }
              placeholder={t("companies.employees.form.spendingLimitPlaceholder")}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label size="xsmall" className="txt-compact-small font-medium">
              {t("companies.employees.form.adminAccess")}
            </Label>
            <CoolSwitch
              fieldName="is_admin"
              label={t("companies.employees.form.isAdmin")}
              description={t("companies.employees.form.isAdminDesc")}
              checked={formData.is_admin || false}
              onChange={(checked) =>
                setFormData({ ...formData, is_admin: checked })
              }
              tooltip={t("companies.employees.form.isAdminTooltip")}
            />
          </div>
        </div>
      </Drawer.Body>
      <Drawer.Footer>
        <Drawer.Close asChild>
          <Button variant="secondary">{t("actions.cancel")}</Button>
        </Drawer.Close>
        <Button type="submit" disabled={loading}>
          {loading ? t("companies.employees.form.saving") : t("companies.employees.form.save")}
        </Button>
        {error && <Text className="text-red-500">{t("companies.employees.create.error")}</Text>}
      </Drawer.Footer>
    </form>
  )
}
