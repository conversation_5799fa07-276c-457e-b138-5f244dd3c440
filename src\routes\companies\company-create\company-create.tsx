import { Container, Head<PERSON> } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { SingleColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { CompanyCreateForm } from "./components/company-create-form"

export const CompanyCreate = () => {
  const { t } = useTranslation()
  const { getWidgets } = useExtension()

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("company.create.after"),
        before: getWidgets("company.create.before"),
      }}
    >
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 pb-0">
          <Heading className="font-sans font-medium h1-core">
            {t("companies.create.title", "Create Company")}
          </Heading>
        </div>
        <CompanyCreateForm />
      </Container>
    </SingleColumnPage>
  )
}