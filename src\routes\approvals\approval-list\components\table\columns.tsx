import { createColumnHelper } from "@tanstack/react-table"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { TextCell } from "../../../../../components/table/table-cells/common/text-cell"
import { MoneyAmountCell } from "../../../../../components/table/table-cells/common/money-amount-cell"
import { ApprovalStatusBadge } from "../approval-status-badge"
import { ItemsPopover } from "../items-popover"

// Custom date time display component, consistent with quote page
const DateTimeCell = ({ date }: { date: string }) => {
  if (!date) return <TextCell text="-" />

  const dateObj = new Date(date)

  // Format as YYYY-MM-DD HH:mm:ss
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')

  const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

  return <TextCell text={formatted} />
}

// Type definition based on actual API response data structure
type CartWithApproval = {
  id: string
  email: string
  created_at: string
  updated_at: string
  items: Array<{
    unit_price: number
    quantity: number
  }>
  approvals: Array<{
    id: string
    type: string
    status: string
    created_at: string
    updated_at: string
  }>
  approval_status: {
    status: string
  }
  company: {
    name: string
  }
}

const columnHelper = createColumnHelper<CartWithApproval>()

export const useApprovalsTableColumns = () => {
  const { t } = useTranslation()

  return useMemo(
    () => [
      columnHelper.accessor("id", {
        header: t("approvals.table.id", "ID"),
        cell: ({ getValue }) => {
          const id = getValue()
          return (
            <span className="font-mono text-sm">
              {id.slice(-8).toUpperCase()}
            </span>
          )
        },
      }),

      columnHelper.accessor((row) => row.company?.name, {
        id: "company.name",
        header: t("approvals.table.company", "Company"),
        cell: ({ getValue }) => <TextCell text={getValue() || "-"} />,
      }),
      columnHelper.accessor("email", {
        header: t("approvals.table.customer", "Customer"),
        cell: ({ getValue }) => {
          const email = getValue()
          return <TextCell text={email || "-"} />
        },
      }),
      columnHelper.accessor("items", {
        id: "amount",
        header: t("approvals.table.amount", "Amount"),
        cell: ({ getValue }) => {
          const items = getValue()
          if (!items || items.length === 0) return <TextCell text="-" />

          // Calculate total amount
          const totalAmount = items.reduce((sum, item) => {
            return sum + (item.unit_price * item.quantity)
          }, 0)

          return <MoneyAmountCell amount={totalAmount} currencyCode="EUR" />
        },
      }),
      columnHelper.accessor("approval_status.status", {
        header: t("approvals.table.status", "Status"),
        cell: ({ getValue }) => <ApprovalStatusBadge status={getValue()} />,
      }),
      columnHelper.accessor("items", {
        header: t("fields.items", "Items"),
        cell: ({ getValue }) => {
          const items = getValue()
          return <ItemsPopover items={items} currencyCode="EUR" />
        },
      }),
      columnHelper.accessor("created_at", {
        header: t("approvals.table.createdAt", "Created At"),
        cell: ({ getValue }) => <DateTimeCell date={getValue()} />,
      }),
      columnHelper.accessor("updated_at", {
        header: t("fields.updatedAt", "Updated At"),
        cell: ({ getValue }) => <DateTimeCell date={getValue()} />,
      }),
    ],
    [t]
  )
}
