import { UIMatch } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { AdminCompanyResponse } from "../../../types"

type CompanyDetailBreadcrumbProps = UIMatch<AdminCompanyResponse>

export const Breadcrumb = (props: CompanyDetailBreadcrumbProps) => {
  const { t } = useTranslation()
  
  const company = props.data?.company
  
  if (!company) {
    return t("companies.title", "Companies")
  }

  return `${t("companies.title", "Companies")} / ${company.name}`
}