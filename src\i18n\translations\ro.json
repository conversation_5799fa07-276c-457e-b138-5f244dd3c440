{"$schema": "./$schema.json", "general": {"ascending": "Ascendent", "descending": "Descendent", "add": "Adăuga", "start": "Început", "end": "Sfârşit", "open": "Deschide", "close": "<PERSON><PERSON><PERSON>", "apply": "Aplicați", "range": "Gamă", "search": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "results": "rezultate", "pages": "pagini", "next": "Următorul", "prev": "Prev", "is": "este", "timeline": "Cronologie", "success": "Succes", "warning": "Avertizare", "tip": "Sfat", "error": "Eroare", "select": "Selecta", "selected": "Selectat", "enabled": "Activat", "disabled": "Dezactivat", "expired": "Expirat", "active": "Activ", "revoked": "Revocat", "new": "Nou", "modified": "Modificat", "added": "Adă<PERSON><PERSON>", "removed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Admin", "store": "Magazin", "details": "<PERSON><PERSON><PERSON>", "countSelected": "{{count}} selectat", "countOfTotalSelected": "{{count}} de {{total}} selectat", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} <PERSON> mult", "areYouSure": "esti sigur?", "noRecordsFound": "Nu au fost găsite înregistrări", "typeToConfirm": "Introdu {val} pentru a confirma:", "noResultsTitle": "<PERSON><PERSON><PERSON> rezultat", "noResultsMessage": "Încercați să schimbați filtrele sau interogarea de căutare", "noSearchResults": "<PERSON><PERSON>un rezultat al căutării", "noSearchResultsFor": "Nu există rezultate de căutare pentru <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON><PERSON><PERSON>", "noRecordsMessage": "Nu există înregistrări de afișat", "unsavedChangesTitle": "Sigur doriți să părăsiți acest formular?", "unsavedChangesDescription": "Aveți modificări nesalvate care se vor pierde dacă părăsiți acest formular.", "includesTaxTooltip": "Prețurile din această coloană includ taxe.", "excludesTaxTooltip": "Prețurile din această coloană sunt fără taxe.", "noMoreData": "Nu mai sunt date", "items_one": "{{count}} articol", "items_other": "{{count}} articole"}, "json": {"header": "JSON", "drawer": {"description": "Vizualizați datele JSON pentru acest obiect.", "header_one": "JSON <0>· {{count}} cheie</0>", "header_other": "JSON <0>· {{count}} chei</0>"}, "numberOfKeys_one": "{{count}} cheie", "numberOfKeys_other": "{{count}} chei"}, "metadata": {"header": "Metadate", "edit": {"header": "Editați metadatele", "description": "Editați metadatele pentru acest obiect.", "successToast": "Metadatele au fost actualizate cu succes.", "actions": {"insertRowAbove": "Introduceți rândul de deasupra", "insertRowBelow": "Introduceți rândul de mai jos", "deleteRow": "Ștergeți rândul"}, "labels": {"key": "<PERSON><PERSON><PERSON>", "value": "Valoare"}, "complexRow": {"label": "<PERSON><PERSON> sunt dezacti<PERSON>", "description": "Acest obiect conține metadate non-primitive, cum ar fi matrice sau obiecte, care nu pot fi editate aici. ", "tooltip": "Acest rând este dezactivat deoarece conține date neprimitive."}}, "numberOfKeys_one": "{{count}} cheie", "numberOfKeys_other": "{{count}} chei"}, "validation": {"mustBeInt": "Valoarea trebuie să fie un număr întreg.", "mustBePositive": "Valoarea trebuie să fie un număr pozitiv."}, "actions": {"save": "<PERSON><PERSON>", "saveAsDraft": "Salvați ca schiță", "copy": "<PERSON><PERSON>", "copied": "Copiat", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "publish": "Publica", "create": "<PERSON><PERSON>", "delete": "Şterge", "remove": "Elimina", "revoke": "Revoca", "cancel": "<PERSON><PERSON>", "forceConfirm": "Confirmare forțată", "continueEdit": "Continuați editarea", "enable": "Permite", "disable": "Dezactiva<PERSON><PERSON>", "undo": "<PERSON><PERSON>", "complete": "Complet", "viewDetails": "<PERSON><PERSON><PERSON> de<PERSON>", "back": "Spate", "close": "<PERSON><PERSON><PERSON>", "showMore": "Arat<PERSON> mai multe", "continue": "Continua", "continueWithEmail": "Continuați cu e-mail", "idCopiedToClipboard": "ID copiat în clipboard", "addReason": "Adăugați un motiv", "addNote": "Adăugați o notă", "reset": "Resetați", "confirm": "Confirma", "edit": "<PERSON><PERSON>", "addItems": "Adăugați articole", "download": "Descă<PERSON><PERSON>i", "clear": "<PERSON><PERSON>", "clearAll": "Ștergeți totul", "apply": "Aplicați", "add": "Adăuga", "select": "Selecta", "browse": "Răsfoiește", "logout": "Deconectare", "hide": "Ascunde", "export": "Export", "import": "Import", "cannotUndo": "Această acțiune nu poate fi anulată"}, "operators": {"in": "În"}, "app": {"search": {"label": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Căutați în întregul magazin, inclusiv comenzi, produse, clienți și multe altele.", "allAreas": "Toate zonele", "navigation": "Navigare", "openResult": "Deschide rezultatul", "showMore": "Arat<PERSON> mai multe", "placeholder": "Sari la sau gaseste orice...", "noResultsTitle": "Nu s-au găsit rezultate", "noResultsMessage": "Nu am găsit nimic care să corespundă căutării dvs.", "emptySearchTitle": "Tastați pentru a căuta", "emptySearchMessage": "Introduceți un cuvânt cheie sau o expresie de explorat.", "loadMore": "Î<PERSON><PERSON><PERSON> {{count}} Mai mult", "groups": {"all": "Toate zonele", "customer": "Clien<PERSON><PERSON>", "customerGroup": "Grupuri <PERSON>", "product": "Produse", "productVariant": "<PERSON><PERSON><PERSON> de produs", "inventory": "Inventar", "reservation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "Categorii", "collection": "Colecții", "order": "<PERSON><PERSON><PERSON>", "promotion": "Promoții", "campaign": "Campanii", "priceList": "Liste de prețuri", "user": "Util<PERSON><PERSON><PERSON>", "region": "Regiunile", "taxRegion": "Regiuni fiscale", "returnReason": "Motive de întoarcere", "salesChannel": "Canale de vânzare", "productType": "<PERSON><PERSON><PERSON> de produse", "productTag": "Etichete de produs", "location": "Locații", "shippingProfile": "<PERSON><PERSON><PERSON>pedier<PERSON>", "publishableApiKey": "Chei API care pot fi publicate", "secretApiKey": "Chei secrete API", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navigare"}}, "keyboardShortcuts": {"pageShortcut": "<PERSON>ri la", "settingShortcut": "<PERSON><PERSON><PERSON>", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "apoi", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON>", "goToProducts": "Produse", "goToCollections": "Colecții", "goToCategories": "Categorii", "goToCustomers": "Clien<PERSON><PERSON>", "goToCustomerGroups": "Grupuri <PERSON>", "goToInventory": "Inventar", "goToReservations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToPriceLists": "Liste de prețuri", "goToPromotions": "Promoții", "goToCampaigns": "Campanii"}, "settings": {"goToSettings": "<PERSON><PERSON><PERSON>", "goToStore": "Magazin", "goToUsers": "Util<PERSON><PERSON><PERSON>", "goToRegions": "Regiunile", "goToTaxRegions": "Regiuni fiscale", "goToSalesChannels": "Canale de vânzare", "goToProductTypes": "<PERSON><PERSON><PERSON> de produse", "goToLocations": "Locații", "goToPublishableApiKeys": "Chei API care pot fi publicate", "goToSecretApiKeys": "Chei secrete API", "goToWorkflows": "Fluxuri de lucru", "goToProfile": "Profil", "goToReturnReasons": "Motive de returnare"}}, "menus": {"user": {"documentation": "Documentare", "changelog": "Jurnalul modificărilor", "shortcuts": "Comenzi rapide", "profileSettings": "<PERSON><PERSON><PERSON>l", "theme": {"label": "Temă", "dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "system": "Sistem"}}, "store": {"label": "Magazin", "storeSettings": "Memorează setările"}, "actions": {"logout": "Deconectați-vă"}}, "nav": {"accessibility": {"title": "Navigare", "description": "Meniul de navigare pentru tabloul de bord."}, "common": {"extensions": "<PERSON><PERSON><PERSON>"}, "main": {"store": "Magazin", "storeSettings": "Memorează setările"}, "settings": {"header": "<PERSON><PERSON><PERSON>", "general": "General", "developer": "Dezvoltator", "myAccount": "Contul meu"}}}, "dataGrid": {"columns": {"view": "<PERSON><PERSON><PERSON>", "resetToDefault": "Resetați la valoarea implicită", "disabled": "Modificarea coloanelor care sunt vizibile este dezactivată."}, "shortcuts": {"label": "Comenzi rapide", "commands": {"undo": "<PERSON><PERSON>", "redo": "Reface", "copy": "<PERSON><PERSON>", "paste": "Pastă", "edit": "<PERSON><PERSON>", "delete": "Şterge", "clear": "<PERSON><PERSON>", "moveUp": "Mișcă-te în sus", "moveDown": "Deplasați-vă în jos", "moveLeft": "Deplasați-vă la stânga", "moveRight": "Deplasați-vă la dreapta", "moveTop": "Mutați sus", "moveBottom": "Mutați în jos", "selectDown": "Selectați în jos", "selectUp": "Selectați sus", "selectColumnDown": "Selectați coloana în jos", "selectColumnUp": "Selectați coloana în sus", "focusToolbar": "Bara de instrumente Focus", "focusCancel": "Anula<PERSON>i focalizarea"}}, "errors": {"fixError": "Remediați eroarea", "count_one": "{{count}} eroare", "count_other": "{{count}} erori"}}, "filters": {"sortLabel": "Sortare", "filterLabel": "Filtrare", "searchLabel": "<PERSON><PERSON><PERSON><PERSON>", "date": {"today": "<PERSON><PERSON><PERSON><PERSON>", "lastSevenDays": "Ultimele 7 zile", "lastThirtyDays": "Ultimele 30 de zile", "lastNinetyDays": "Ultimele 90 de zile", "lastTwelveMonths": "Ultimele 12 luni", "custom": "Personalizat", "from": "<PERSON>", "to": "La", "starting": "Pornire", "ending": "Final"}, "compare": {"lessThan": "<PERSON> de<PERSON>", "greaterThan": "<PERSON> mare decât", "exact": "Corect", "range": "Gamă", "lessThanLabel": "mai pu<PERSON>in dec<PERSON>t {{value}}", "greaterThanLabel": "mai mare decât {{value}}", "andLabel": "<PERSON>i"}, "sorting": {"alphabeticallyAsc": "A to Z", "alphabeticallyDesc": "Z to A", "dateAsc": "Cel mai nou primul", "dateDesc": "Cel mai vechi primul"}, "radio": {"yes": "Da", "no": "<PERSON>u", "true": "<PERSON><PERSON><PERSON>", "false": "Fals"}, "addFilter": "Adăugați filtru"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON>", "badRequestMessage": "Cererea nu a putut fi înțeleasă de server din cauza sintaxei incorecte.", "notFoundTitle": "404 - Nu există nicio pagină la această adresă", "notFoundMessage": "Verificați adresa URL și încercați din nou sau utilizați bara de căutare pentru a găsi ceea ce căutați.", "internalServerErrorTitle": "500 - Eroare internă de server", "internalServerErrorMessage": "A apărut o eroare neașteptată pe server. ", "defaultTitle": "A apărut o eroare", "defaultMessage": "A apărut o eroare neașteptată la redarea acestei pagini.", "noMatchMessage": "Pagina pe care o cauți nu există.", "backToDashboard": "Înapoi la tabloul de bord"}, "addresses": {"shippingAddress": {"header": "Adresa de transport", "editHeader": "Editați adresa de livrare", "editLabel": "Adresa de transport", "label": "Adresa de transport"}, "billingAddress": {"header": "Adresa de facturare", "editHeader": "Editați adresa de facturare", "editLabel": "Adresa de facturare", "label": "Adresa de facturare", "sameAsShipping": "La fel ca adresa de livrare"}, "contactHeading": "Contact", "locationHeading": "Locaţie"}, "email": {"editHeader": "Editați e-mailul", "editLabel": "E-mail", "label": "E-mail"}, "transferOwnership": {"header": "Transferați proprietatea", "label": "Transferați proprietatea", "details": {"order": "Det<PERSON><PERSON> comanda", "draft": "Ciorn<PERSON>"}, "currentOwner": {"label": "Actual proprietar", "hint": "Actualul proprietar al comenzii."}, "newOwner": {"label": "Noul proprietar", "hint": "Noul proprietar căruia îi transferă comanda."}, "validation": {"mustBeDifferent": "Noul proprietar trebuie să fie diferit de proprietarul actual.", "required": "Este necesar un nou proprietar."}}, "sales_channels": {"availableIn": "Disponibil în <0>{{x}}</0> de <1>{{y}}</1> canale de v<PERSON><PERSON>e"}, "products": {"domain": "Produse", "list": {"noRecordsMessage": "Creați primul dvs. produs pentru a începe să vindeți."}, "edit": {"header": "Editați produsul", "description": "Editați detaliile produsului.", "successToast": "Produs {{title}} a fost actualizat cu succes."}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON> produs", "description": "Creați un produs nou.", "header": "General", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organiza", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Truse de inventar"}, "errors": {"variants": "Vă rugăm să selectați cel puțin o variantă.", "options": "Vă rugăm să creați cel puțin o opțiune.", "uniqueSku": "SKU trebuie să fie unic."}, "inventory": {"heading": "Truse de inventar", "label": "Adăugați articole de inventar la kitul de inventar al variantei.", "itemPlaceholder": "Selectați obiectul de inventar", "quantityPlaceholder": "Câte dintre acestea sunt necesare pentru kit?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON>, acesta este un produs cu variante", "subHeadingDescription": "<PERSON><PERSON><PERSON>, vom crea o variantă implicită pentru dvs", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "<PERSON><PERSON>, Mijlocii, Mari"}, "productVariants": {"label": "<PERSON><PERSON><PERSON> de produs", "hint": "Acest clasament va afecta ordinea variantelor din vitrina dvs.", "alert": "Adăugați opțiuni pentru a crea variante.", "tip": "Variantele rămase nebifate nu vor fi create. "}, "productOptions": {"label": "Opțiuni de produs", "hint": "Definiți opțiunile pentru produs, de ex. "}}, "successToast": "Produs {{title}} a fost creat cu succes."}, "export": {"header": "Exportați lista de produse", "description": "Exportați lista de produse într-un fișier CSV.", "success": {"title": "Procesăm exportul dvs", "description": "Exportul datelor poate dura câteva minute. "}, "filters": {"title": "Filtre", "description": "Aplicați filtre în prezentarea generală a tabelului pentru a ajusta această vizualizare"}, "columns": {"title": "<PERSON><PERSON><PERSON>", "description": "Personalizați datele exportate pentru a răspunde nevoilor specifice"}}, "import": {"header": "Importați lista de produse", "uploadLabel": "Import produse", "uploadHint": "Trageți și plasați un fișier CSV sau faceți clic pentru a încărca", "description": "Importați produse furnizând un fișier CSV într-un format predefinit", "template": {"title": "Nu sunteți sigur cum să vă aranjați lista?", "description": "Descărcați șablonul de mai jos pentru a vă asigura că urmați formatul corect."}, "upload": {"title": "Încărcați un fișier CSV", "description": "Prin importuri puteți adăuga sau actualiza produse. ", "preprocessing": "Preprocesare...", "productsToCreate": "Produsele vor fi create", "productsToUpdate": "Produsele vor fi actualizate"}, "success": {"title": "Procesăm importul dvs", "description": "Importarea datelor poate dura ceva timp. "}}, "deleteWarning": "Sunteți pe cale să ștergeți produsul {{title}}. ", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "<PERSON><PERSON>a", "description": "Nu există variante de afișat."}, "filtered": {"heading": "<PERSON><PERSON><PERSON> rezultat", "description": "Nicio variantă nu corespunde criteriilor actuale de filtrare."}}, "attributes": "Atribute", "editAttributes": "Editați atribute", "editOptions": "Editare opțiuni", "editPrices": "Editați preț<PERSON>", "media": {"label": "Media", "editHint": "Adăugați conținut media la produs pentru a-l prezenta în vitrina dvs.", "makeThumbnail": "Faceți o miniatură", "uploadImagesLabel": "Încărcați imagini", "uploadImagesHint": "Trageți și plasați imaginile aici sau faceți clic pentru a încărca.", "invalidFileType": "'{{name}}' nu este un tip de fișier acceptat.  {{types}}.", "failedToUpload": "Nu s-a putut încărca conținutul media adăugat. ", "thumbnailTooltip": "Miniatură", "galleryLabel": "Galerie", "downloadImageLabel": "Descărcați imaginea curentă", "deleteImageLabel": "Ștergeți imaginea cure<PERSON>", "emptyState": {"header": "Încă nu există media", "description": "Adăugați conținut media la produs pentru a-l prezenta în vitrina dvs.", "action": "Adăugați conținut media"}, "successToast": "Media a fost actualizată cu succes.", "deleteWarning_one": "Sunteți pe cale să ștergeți {{count}} imagine. ", "deleteWarning_other": "Sunteți pe cale să ștergeți {{count}} imagini. ", "deleteWarningWithThumbnail_one": "Sunteți pe cale să ștergeți {{count}} imaginea inclusiv miniatura. ", "deleteWarningWithThumbnail_other": "Sunteți pe cale să ștergeți {{count}} imagini, inclusiv miniatura. "}, "discountableHint": "Dacă nu este bifată, reducerile nu vor fi aplicate acestui produs.", "noSalesChannels": "Nu este disponibil în niciun canal de vânzare", "deleteVariantWarning": "Sunteți pe cale să ștergeți varianta {{title}}. ", "productStatus": {"draft": "Proiect", "published": "Publicat", "proposed": "Propus", "rejected": "Respins"}, "fields": {"title": {"label": "Titlu", "hint": "Dați produsului dvs. un titlu scurt și clar.<0/>50-60 de caractere este lungimea recomandată pentru motoarele de căutare.", "placeholder": "Jachetă de iarnă"}, "subtitle": {"label": "Subtitlu", "placeholder": "Cald și confortabil"}, "handle": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Mânerul este folosit pentru a face referire la produsul din vitrina dvs. ", "placeholder": "jachetă-de-iarnă"}, "description": {"label": "Des<PERSON><PERSON><PERSON>", "hint": "Oferiți produsului dumneavoastră o descriere scurtă și clară.<0/>120-160 de caractere este lungimea recomandată pentru motoarele de căutare.", "placeholder": "O jachetă călduroasă și confortabilă"}, "discountable": {"label": "Reducere", "hint": "Dacă nu este bifată, reducerile nu vor fi aplicate acestui produs"}, "type": {"label": "Tip"}, "collection": {"label": "Colectare"}, "categories": {"label": "Categorii"}, "tags": {"label": "Etichete"}, "sales_channels": {"label": "Canale de vânzare", "hint": "Acest produs va fi disponibil numai în canalul prestabilit de vânzări dacă este lăsat neatins."}, "countryOrigin": {"label": "Țara de origine"}, "material": {"label": "Material"}, "width": {"label": "Lăţime"}, "length": {"label": "Lungime"}, "height": {"label": "Înălţime"}, "weight": {"label": "Greutate"}, "options": {"label": "Opțiuni de produs", "hint": "Opțiunile sunt folosite pentru a defini culoarea, dimensiunea etc. a produsului", "add": "Adăugați opțiunea", "optionTitle": "Titlul opțiunii", "optionTitlePlaceholder": "<PERSON><PERSON><PERSON>", "variations": "Variante (separate prin virgulă)", "variantionsPlaceholder": "Roșu, Albastru, Verde"}, "variants": {"label": "<PERSON><PERSON><PERSON> de produs", "hint": "Variantele rămase nebifate nu vor fi create. Această clasare va afecta modul în care variantele sunt clasate în interfața dvs."}, "mid_code": {"label": "Cod mi<PERSON><PERSON><PERSON>"}, "hs_code": {"label": "Cod HS"}}, "variant": {"edit": {"header": "Editați varianta", "success": "Varianta de produs editată cu succes"}, "create": {"header": "Detalii variante"}, "deleteWarning": "<PERSON><PERSON><PERSON> doriți să ștergeți această variantă?", "pricesPagination": "1 - {{current}} de {{total}} preturi", "tableItemAvailable": "{{availableCount}} disponibil", "inventory": {"notManaged": "Nu este gestionat", "manageItems": "Gestionați articolele de inventar", "notManagedDesc": "Inventarul nu este gestionat pentru această variantă. ", "manageKit": "Gestionați setul de inventar", "navigateToItem": "Accesați obiectul de inventar", "actions": {"inventoryItems": "Accesați obiectul de inventar", "inventoryKit": "Afișați articolele de inventar"}, "inventoryKit": "<PERSON> in<PERSON>ar", "inventoryKitHint": "Această variantă constă din mai multe articole de inventar?", "validation": {"itemId": "Vă rugăm să selectați articolul de inventar.", "quantity": "Cantitatea este necesară. "}, "header": "Stoc și inventar", "editItemDetails": "Editați detaliile articolului", "manageInventoryLabel": "Gestionați inventarul", "manageInventoryHint": "Când este activată, vom modifica cantitatea de inventar pentru dvs. când sunt create comenzi și retururi.", "allowBackordersLabel": "Permite comenzi în așteptare", "allowBackordersHint": "Când este activat, clienții pot achiziționa varianta chiar dacă nu există o cantitate disponibilă.", "toast": {"levelsBatch": "Nivelurile de inventar au fost actualizate.", "update": "Articolul din inventar a fost actualizat cu succes.", "updateLevel": "Nivelul inventarului a fost actualizat cu succes.", "itemsManageSuccess": "Articolele din inventar au fost actualizate cu succes."}}, "tableItem_one": "{{availableCount}} disponibil la {{locationCount}} locaţie", "tableItem_other": "{{availableCount}} disponibil la {{locationCount}} locații"}, "options": {"header": "Opțiuni", "edit": {"header": "Editare opțiune", "successToast": "Opţiune {{title}} a fost actualizat cu succes."}, "create": {"header": "<PERSON><PERSON><PERSON>", "successToast": "Opţiune {{title}} a fost creat cu succes."}, "deleteWarning": "Sunteți pe cale să ștergeți opțiunea de produs: {{title}}. "}, "organization": {"header": "Organiza", "edit": {"header": "Editați organizația", "toasts": {"success": "Am actualizat cu succes organizarea {{title}}."}}}, "stock": {"heading": "Gestionați nivelurile stocurilor de produse și locațiile", "description": "Actualizați nivelurile de inventar stocate pentru toate variantele de produs.", "loading": "Așteaptă asta poate dura un moment...", "tooltips": {"alreadyManaged": "Acest articol de inventar este deja editabil sub {{title}}.", "alreadyManagedWithSku": "Acest articol de inventar este deja editabil sub {{title}} ({{sku}})."}}, "toasts": {"delete": {"success": {"header": "Produs șters", "description": "{{title}} a fost șters cu succes."}, "error": {"header": "Produsul nu a putut fi șters"}}}, "variantCount_one": "{{count}} variantă", "variantCount_other": "{{count}} variante"}, "collections": {"domain": "Colecții", "subtitle": "Organizați produsele în colecții.", "createCollection": "Creați o colecție", "createCollectionHint": "Creați o nouă colecție pentru a vă organiza produsele.", "createSuccess": "Colecție creată cu succes.", "editCollection": "Editați colecția", "handleTooltip": "Mânerul este folosit pentru a face referire la colecția din vitrina dvs. ", "deleteWarning": "Sunteți pe cale să ștergeți colecția {{title}}. ", "removeSingleProductWarning": "Sunteți pe cale să eliminați produsul {{title}} din colectie. ", "products": {"list": {"noRecordsMessage": "Nu există produse în colecție."}, "add": {"successToast_one": "Produsul a fost adăugat cu succes la colecție.", "successToast_other": "Produsele au fost adăugate cu succes la colecție."}, "remove": {"successToast_one": "Produsul a fost eliminat cu succes din colecție.", "successToast_other": "Produsele au fost eliminate cu succes din colecție."}}, "removeProductsWarning_one": "Sunteți pe cale să eliminați {{count}} produs din colecție. ", "removeProductsWarning_other": "Sunteți pe cale să eliminați {{count}} produse din colectie. "}, "categories": {"domain": "Categorii", "subtitle": "Organizați produsele pe categorii și gestionați clasarea și ierarhia acestor categorii.", "create": {"header": "Creați o categorie", "hint": "Creați o categorie nouă pentru a vă organiza produsele.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizați clasamentul"}, "successToast": "Categorie {{name}} a fost creat cu succes."}, "edit": {"header": "Editați categoria", "description": "Editați categoria pentru a-i actualiza detaliile.", "successToast": "Categoria a fost actualizată cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți categoria {{name}}. ", "successToast": "Categorie {{name}} a fost șters cu succes."}, "products": {"add": {"disabledTooltip": "Produsul este deja în această categorie.", "successToast_one": "Ad<PERSON><PERSON><PERSON> {{count}} produs la categorie.", "successToast_other": "<PERSON><PERSON><PERSON>t {{count}} produse la categorie."}, "remove": {"confirmation_one": "Sunteți pe cale să eliminați {{count}} produs din categorie. ", "confirmation_other": "Sunteți pe cale să eliminați {{count}} produse din categorie. ", "successToast_one": "Îndepărtat {{count}} produs din categorie.", "successToast_other": "Îndepărtat {{count}} produse din categorie."}, "list": {"noRecordsMessage": "Nu există produse în categorie."}}, "organize": {"header": "Organiza", "action": "Editați clasamentul"}, "fields": {"visibility": {"label": "Vizibilitate", "internal": "Intern", "public": "Public"}, "status": {"label": "Stare", "active": "Activ", "inactive": "Inactiv"}, "path": {"label": "<PERSON>", "tooltip": "Afișați calea completă a categoriei."}, "children": {"label": "<PERSON><PERSON><PERSON>"}, "new": {"label": "Nou"}}}, "inventory": {"domain": "Inventar", "subtitle": "Gestionați-vă articolele de inventar", "reserved": "Rezervat", "available": "Disponibil", "locationLevels": "Locații", "associatedVariants": "Variante asociate", "manageLocations": "Gestionați locațiile", "deleteWarning": "Sunteți pe cale să ștergeți un articol de inventar. ", "editItemDetails": "Editați detaliile articolului", "create": {"title": "Creați un articol de inventar", "details": "<PERSON><PERSON><PERSON>", "availability": "Disponibilitate", "locations": "Locații", "attributes": "Atribute", "requiresShipping": "Necesită transport", "requiresShippingHint": "Articolul din inventar necesită livrare?", "successToast": "Elementul de inventar a fost creat cu succes."}, "reservation": {"header": "<PERSON>zervar<PERSON> de {{itemName}}", "editItemDetails": "Editați rezervar<PERSON>", "lineItemId": "ID-ul elementului rând", "orderID": "ID comandă", "description": "Des<PERSON><PERSON><PERSON>", "location": "Locaţie", "inStockAtLocation": "În stoc la această locație", "availableAtLocation": "Disponibil în această locație", "reservedAtLocation": "Rezervat în această locație", "reservedAmount": "<PERSON><PERSON> de rezerv<PERSON>", "create": "<PERSON>rea<PERSON><PERSON> rezervar<PERSON>", "itemToReserve": "Articol de rezervat", "quantityPlaceholder": "<PERSON><PERSON>t vrei să rezervi?", "descriptionPlaceholder": "Ce tip de rezervare este aceasta?", "successToast": "Rezervarea a fost creată cu succes.", "updateSuccessToast": "Rezervarea a fost actualizată cu succes.", "deleteSuccessToast": "Rezervarea a fost ștearsă cu succes.", "errors": {"noAvaliableQuantity": "Locația stocului nu are cantitate disponibilă.", "quantityOutOfRange": "Cantitatea minimă este 1 și cantitatea maximă este {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Cantitatea stocată nu poate fi actualizată la mai puțin decât cantitatea rezervată de {{quantity}}."}}, "toast": {"updateLocations": "Locațiile au fost actualizate cu succes.", "updateLevel": "Nivelul inventarului a fost actualizat cu succes.", "updateItem": "Articolul din inventar a fost actualizat cu succes."}, "stock": {"title": "Actualizați nivelurile de inventar", "description": "Actualizați nivelurile de inventar stocate pentru articolele de inventar selectate.", "action": "Editați nivel<PERSON> s<PERSON>", "placeholder": "Nu este activat", "disablePrompt_one": "Sunteți pe cale să dezactivați {{count}} nivelul locației. Această acțiune nu poate fi anulată.", "disablePrompt_other": "Sunteți pe cale să dezactivați {{count}} niveluri de locație. Această acțiune nu poate fi anulată.", "disabledToggleTooltip": "Nu se poate dezactiva: ștergeți cantitatea primită și/sau rezervată înainte de dezactivare.", "successToast": "Nivelurile de inventar au fost actualizate cu succes."}}, "giftCards": {"domain": "<PERSON><PERSON> cadou", "editGiftCard": "Editați cardul cadou", "createGiftCard": "Creați un card cadou", "createGiftCardHint": "Creați manual un card cadou care poate fi folosit ca metodă de plată în magazinul dvs.", "selectRegionFirst": "Selectați mai întâi o regiune", "deleteGiftCardWarning": "Sunteți pe cale să ștergeți cardul cadou {{code}}. ", "balanceHigherThanValue": "Soldul nu poate fi mai mare decât suma inițială.", "balanceLowerThanZero": "Soldul nu poate fi negativ.", "expiryDateHint": "Țările au legi diferite cu privire la datele de expirare a cardurilor cadou. ", "regionHint": "Schimbarea regiunii cardului cadou va schimba, de asem<PERSON>a, moneda acestuia, ceea ce poate afecta valoarea sa monetară.", "enabledHint": "Specificați dacă cardul cadou este activat sau dezactivat.", "balance": "Echilibru", "currentBalance": "Sold curent", "initialBalance": "Echilibrul inițial", "personalMessage": "<PERSON><PERSON> personal", "recipient": "Destinatar"}, "customers": {"domain": "Clien<PERSON><PERSON>", "list": {"noRecordsMessage": "Clienții tăi vor apărea aici."}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON> client", "hint": "Creați un client nou și gestionați detaliile acestuia.", "successToast": "Client {{email}} a fost creat cu succes."}, "groups": {"label": "Grupuri <PERSON>", "remove": "<PERSON><PERSON><PERSON> doriți să eliminați clientul din \"{{name}}„grup de clienți?", "removeMany": "<PERSON><PERSON><PERSON> do<PERSON>i să faceți clienți din următoarele grupuri de clienți: {{groups}}?", "alreadyAddedTooltip": "Clientul se află deja în acest grup de clienți.", "list": {"noRecordsMessage": "Acest client nu aparține niciunui grup."}, "add": {"success": "Client adăugat la: {{groups}}.", "list": {"noRecordsMessage": "Creați mai întâi un grup de clienți."}}, "removed": {"success": "Client eliminat din: {{groups}}.", "list": {"noRecordsMessage": "Creați mai întâi un grup de clienți."}}}, "edit": {"header": "Editați clientul", "emailDisabledTooltip": "Adresa de e-mail nu poate fi schimbată pentru clienții înregistrați.", "successToast": "Client {{email}} a fost actualizat cu succes."}, "delete": {"title": "Ștergeți clientul", "description": "Sunteți pe cale să ștergeți clientul {{email}}. ", "successToast": "Client {{email}} a fost șters cu succes."}, "fields": {"guest": "Oaspete", "registered": "Înregistrat", "groups": "<PERSON><PERSON><PERSON>"}, "registered": "Înregistrat", "guest": "Oaspete", "hasAccount": "Are cont"}, "customerGroups": {"domain": "Grupuri <PERSON>", "subtitle": "Organizați clienții în grupuri. ", "list": {"empty": {"heading": "Fără grupuri <PERSON>", "description": "Nu există grupuri de clienți de afișat."}, "filtered": {"heading": "<PERSON><PERSON><PERSON> rezultat", "description": "Niciun grup de clienți nu corespunde criteriilor de filtrare curente."}}, "create": {"header": "Creați un grup de clienți", "hint": "Creați un nou grup de clienți pentru a vă segmenta clienții.", "successToast": "Grup de clienți {{name}} a fost creat cu succes."}, "edit": {"header": "Editați grupul de clienți", "successToast": "Grup de clienți {{name}} a fost actualizat cu succes."}, "delete": {"title": "Ștergeți grupul de clienți", "description": "Sunteți pe cale să ștergeți grupul de clienți {{name}}. ", "successToast": "Grup de clienți {{name}} a fost șters cu succes."}, "customers": {"alreadyAddedTooltip": "Clientul a fost deja adăugat în grup.", "add": {"list": {"noRecordsMessage": "Creați mai întâi un client."}, "successToast_one": "Clientul a fost adăugat cu succes la grup.", "successToast_other": "Clienții au fost adăugați cu succes la grup."}, "remove": {"title_one": "Eliminați clientul", "title_other": "Eliminați clienți", "description_one": "Sunteți pe cale să eliminați {{count}} client din grupul de clienți. ", "description_other": "Sunteți pe cale să eliminați {{count}} clienții din grupul de clienți. "}, "list": {"noRecordsMessage": "Acest grup nu are clienți."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON>", "claim": "Revendicare", "exchange": "<PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON>", "cancelWarning": "Sunteți pe cale să anulați comanda {{id}}. ", "orderCanceled": "Comanda a fost anulată cu succes", "onDateFromSalesChannel": "{{date}} din {{salesChannel}}", "list": {"noRecordsMessage": "Comenzile tale vor ap<PERSON>rea aici."}, "status": {"not_paid": "Neplătit", "pending": "În așteptare", "completed": "Terminat", "draft": "Proiect", "archived": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "requires_action": "Necesită acțiune"}, "summary": {"requestReturn": "Solicitați retur", "allocateItems": "Alocați articole", "editOrder": "Editați comanda", "editOrderContinue": "Continuați editarea comenzii", "inventoryKit": "Consta din {{count}}x articole de inventar", "itemTotal": "Total articol", "shippingTotal": "Livrare totală", "discountTotal": "Reducere totală", "taxTotalIncl": "Total impozit (inclus)", "itemSubtotal": "Subtotal articol", "shippingSubtotal": "Subtotal de livrare", "discountSubtotal": "Subtotal reducere", "taxTotal": "Total impozit"}, "transfer": {"title": "Transferați proprietatea", "requestSuccess": "Solicitare de transfer de comandă trimisă la: {{email}}.", "currentOwner": "Actual proprietar", "newOwner": "Noul proprietar", "currentOwnerDescription": "Clientul se referă în prezent la această comandă.", "newOwnerDescription": "Clientului să transfere această comandă."}, "payment": {"title": "Plăți", "isReadyToBeCaptured": "<PERSON><PERSON><PERSON> <0/> este gata pentru a fi capturat.", "totalPaidByCustomer": "Total plătit de client", "capture": "Capturați plata", "capture_short": "Capta", "refund": "Restituire", "markAsPaid": "Marcați ca plătit", "statusLabel": "Starea plății", "statusTitle": "Starea plății", "status": {"notPaid": "Neplătit", "authorized": "Autorizat", "partiallyAuthorized": "Parțial autorizat", "awaiting": "În așteptare", "captured": "<PERSON><PERSON><PERSON>", "partiallyRefunded": "Rambursat parțial", "partiallyCaptured": "<PERSON><PERSON><PERSON><PERSON> capturat", "refunded": "Rambursat", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Necesită acțiune"}, "capturePayment": "Plata de {{amount}} va fi capturat.", "capturePaymentSuccess": "Plata de {{amount}} capturat cu succes", "markAsPaidPayment": "Plata de {{amount}} va fi marcat ca plătit.", "markAsPaidPaymentSuccess": "Plata de {{amount}} marcat cu succes ca plătit", "createRefund": "<PERSON><PERSON><PERSON><PERSON> rambur<PERSON>e", "refundPaymentSuccess": "Rambursarea sumei {{amount}} de succes", "createRefundWrongQuantity": "Cantitatea ar trebui să fie un număr între 1 și {{number}}", "refundAmount": "Restituire {{ amount }}", "paymentLink": "Copiați linkul de plată pentru {{ amount }}", "selectPaymentToRefund": "Selectați plata pentru rambursare"}, "edits": {"title": "Editați comanda", "confirm": "Confirmați Editare", "confirmText": "Sunteți pe cale să confirmați o modificare a comenzii. ", "cancel": "Anulează Editarea", "currentItems": "<PERSON><PERSON><PERSON>", "currentItemsDescription": "Ajustați cantitatea articolului sau eliminați.", "addItemsDescription": "Puteți adăuga articole noi la comandă.", "addItems": "Adăugați articole", "amountPaid": "Suma plătită", "newTotal": "Total nou", "differenceDue": "Diferența datorată", "create": "Editați comanda", "currentTotal": "Total curent", "noteHint": "Adăugați o notă internă pentru editare", "cancelSuccessToast": "Modificarea comenzii a fost anulată", "createSuccessToast": "Solicitarea de modificare a comenzii a fost creată", "activeChangeError": "Există deja o modificare activă a comenzii (retur, revendicare, schimb etc.). ", "panel": {"title": "A fost solicitată modificarea comenzii", "titlePending": "Modificarea comenzii este în așteptare"}, "toast": {"canceledSuccessfully": "Modificarea comenzii a fost anulată", "confirmedSuccessfully": "Modificarea comenzii a fost confirmată"}, "validation": {"quantityLowerThanFulfillment": "Nu se poate seta cantitatea să fie mai mică sau egală cu cantitatea îndeplinită"}}, "edit": {"email": {"title": "Editați e-mailul", "requestSuccess": "E-mail de comandă actualizat la {{email}}."}, "shippingAddress": {"title": "Editați adresa de livrare", "requestSuccess": "Adresa de livrare a comenzii a fost actualizată."}, "billingAddress": {"title": "Editați adresa de facturare", "requestSuccess": "Adresa de facturare a comenzii a fost actualizată."}}, "returns": {"create": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmați retur", "confirmText": "Sunteți pe cale să confirmați o retur. ", "inbound": "Inbound", "outbound": "Ieșire", "sendNotification": "Trimite o notificare", "sendNotificationHint": "Anunțați clientul despre retur.", "returnTotal": "Retur total", "inboundTotal": "Total de intrare", "refundAmount": "<PERSON><PERSON>", "outstandingAmount": "<PERSON><PERSON> restante", "reason": "Motiv", "reasonHint": "Alegeți de ce clientul dorește să returneze articole.", "note": "<PERSON>a", "noInventoryLevel": "<PERSON><PERSON><PERSON><PERSON> nivel de inventar", "noInventoryLevelDesc": "Locația selectată nu are un nivel de inventar pentru articolele selectate. ", "noteHint": "Puteți scrie liber dacă doriți să specificați ceva.", "location": "Locaţie", "locationHint": "Alegeți locația în care doriți să returnați articolele.", "inboundShipping": "Transport de retur", "inboundShippingHint": "Alegeți ce metodă doriți să utilizați.", "returnableQuantityLabel": "Cantitate returnabilă", "refundableAmountLabel": "<PERSON>m<PERSON> rambur<PERSON>", "returnRequestedInfo": "{{requestedItemsCount}}x articole solicitate returnare", "returnReceivedInfo": "{{requestedItemsCount}}x articole returnate primite", "itemReceived": "Articole primite", "returnRequested": "Retur solicitat", "damagedItemReceived": "Articole deteriorate primite", "damagedItemsReturned": "{{quantity}}x articole deteriorate returnate", "activeChangeError": "Există o modificare activă a comenzii în curs pentru această comandă. ", "cancel": {"title": "Anulează retur", "description": "<PERSON><PERSON><PERSON> do<PERSON>ți să anulați cererea de retur?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nu s-au găsit opțiuni de expediere retur", "hint": "Nu au fost create opțiuni de livrare pentru retur pentru locație.  <LinkComponent>Locație și livrare</LinkComponent>."}, "outboundShippingOptions": {"title": "Nu au fost găsite opțiuni de expediere", "hint": "Nu au fost create opțiuni de expediere pentru locație.  <LinkComponent>Locație și livrare</LinkComponent>."}}, "receive": {"action": "Primiți articole", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Reaprovizionați toate articolele", "itemsLabel": "Articole primite", "title": "Primiți articole pentru #{{returnId}}", "sendNotificationHint": "Anunțați clientul despre returnarea primită.", "inventoryWarning": "Vă rugăm să rețineți că vom ajusta automat nivelurile de inventar pe baza informațiilor dvs. de mai sus.", "writeOffInputLabel": "Câte dintre articole sunt deteriorate?", "toast": {"success": "Returul primit cu succes.", "errorLargeValue": "Cantitatea este mai mare decât cantitatea de articol solicitată.", "errorNegativeValue": "Cantitatea nu poate fi o valoare negativă.", "errorLargeDamagedValue": "Cantitatea articolelor deteriorate + cantitatea primită nedeteriorată depășește cantitatea totală de articole la retur. "}}, "toast": {"canceledSuccessfully": "Returul a fost anulat cu succes", "confirmedSuccessfully": "Returul a fost confirmat cu succes"}, "panel": {"title": "Returul a fost inițiat", "description": "Există o cerere de returnare deschisă care trebuie completată"}}, "claims": {"create": "Creați revendicare", "confirm": "Confirmați revendicarea", "confirmText": "Sunteți pe cale să confirmați o revendicare. ", "manage": "Gestionați revendicarea", "outbound": "Ieșire", "outboundItemAdded": "{{itemsCount}}x adăugat prin revendicare", "outboundTotal": "Total de ieșire", "outboundShipping": "Livrare i<PERSON>șire", "outboundShippingHint": "Alegeți ce metodă doriți să utilizați.", "refundAmount": "Diferenta estimata", "activeChangeError": "Există o modificare activă a comenzii pentru această comandă. ", "actions": {"cancelClaim": {"successToast": "Revendicarea a fost anulată cu succes."}}, "cancel": {"title": "Anulează revendicarea", "description": "<PERSON><PERSON>r doriți să anulați revendicarea?"}, "tooltips": {"onlyReturnShippingOptions": "Această listă va consta numai din opțiunile de expediere retur."}, "toast": {"canceledSuccessfully": "Revendicarea a fost anulată cu succes", "confirmedSuccessfully": "Revendicarea a fost confirmată cu succes"}, "panel": {"title": "Revendicare inițiată", "description": "Există o cerere de revendicare deschisă care trebuie completată"}}, "exchanges": {"create": "Creați Exchange", "manage": "Gestionați Exchange", "confirm": "Confirmați schimbul", "confirmText": "Sunteți pe cale să confirmați un schimb. ", "outbound": "Ieșire", "outboundItemAdded": "{{itemsCount}}x ad<PERSON>ugat prin schimb", "outboundTotal": "Total de ieșire", "outboundShipping": "Livrare i<PERSON>șire", "outboundShippingHint": "Alegeți ce metodă doriți să utilizați.", "refundAmount": "Diferenta estimata", "activeChangeError": "Există o modificare activă a comenzii pentru această comandă. ", "actions": {"cancelExchange": {"successToast": "Schimbul a fost anulat cu succes."}}, "cancel": {"title": "Anulați schimbul", "description": "<PERSON><PERSON>r doriți să anulați schimbul?"}, "tooltips": {"onlyReturnShippingOptions": "Această listă va consta numai din opțiunile de expediere retur."}, "toast": {"canceledSuccessfully": "Schimbul a fost anulat cu succes", "confirmedSuccessfully": "Schimbul a fost confirmat cu succes"}, "panel": {"title": "Schimbul a fost inițiat", "description": "Există o cerere de schimb deschisă care trebuie completată"}}, "reservations": {"allocatedLabel": "Alocat", "notAllocatedLabel": "Nealocat"}, "allocateItems": {"action": "Alocați articole", "title": "Alocați articolele de comandă", "locationDescription": "Alegeți locația din care doriți să alocați.", "itemsToAllocate": "Elemente de alocat", "itemsToAllocateDesc": "Selectați numărul de articole pe care doriți să le alocați", "search": "Căutați articole", "consistsOf": "Consta din {{num}}x articole de inventar", "requires": "Necesită {{num}} pe variantă", "toast": {"created": "Articole alocate cu succes"}, "error": {"quantityNotAllocated": "Sunt articole nealocate."}}, "shipment": {"title": "Marcați îndeplinirea expediate", "trackingNumber": "<PERSON><PERSON><PERSON><PERSON>", "addTracking": "Adăugați numărul de urmărire", "sendNotification": "Trimite o notificare", "sendNotificationHint": "Notificați clientul despre această expediere.", "toastCreated": "Expedierea a fost creată cu succes."}, "fulfillment": {"cancelWarning": "Sunteți pe cale să anulați o realizare. ", "markAsDeliveredWarning": "Sunteți pe cale să marcați îndeplinirea ca fiind livrată. ", "unfulfilledItems": "Obiecte neîmplinite", "statusLabel": "Starea de îndeplinire", "statusTitle": "Starea de îndeplinire", "fulfillItems": "Completați articolele", "awaitingFulfillmentBadge": "Așteaptă împlinirea", "requiresShipping": "Necesită transport", "number": "# de îndeplinire{{number}}", "itemsToFulfill": "Obiecte de îndeplinit", "create": "Creați împlinire", "available": "Disponibil", "inStock": "În stoc", "markAsShipped": "Marcați ca fiind expediat", "markAsDelivered": "Marcați ca livrat", "itemsToFulfillDesc": "Alegeți articolele și cantitățile de îndeplinit", "locationDescription": "Alegeți locația din care doriți să completați articolele.", "sendNotificationHint": "Notificați clienții despre îndeplinirea creată.", "methodDescription": "Alegeți o metodă de expediere diferită de cea selectată de client", "error": {"wrongQuantity": "Un singur articol este disponibil pentru îndeplinire", "noItems": "Nu există elemente de îndeplinit.", "wrongQuantity_other": "Cantitatea ar trebui să fie un număr între 1 și {{number}}"}, "status": {"notFulfilled": "Neîmplinit", "partiallyFulfilled": "Parțial îndeplinită", "fulfilled": "împlinit", "partiallyShipped": "Parțial expediat", "shipped": "Expediat", "delivered": "Livrat", "partiallyDelivered": "<PERSON><PERSON>", "partiallyReturned": "Parțial returnat", "returned": "<PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Necesită acțiune"}, "toast": {"created": "Implementare creată cu succes", "canceled": "Îndeplinirea a fost anulată", "fulfillmentShipped": "Nu se poate anula o livrare deja expediată", "fulfillmentDelivered": "Realizare marcată ca livrată cu succes"}, "trackingLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shippingFromLabel": "Livrar<PERSON> de <PERSON>", "itemsLabel": "Articole"}, "refund": {"title": "<PERSON><PERSON><PERSON><PERSON> rambur<PERSON>e", "sendNotificationHint": "Notificați clienții despre rambursarea creată.", "systemPayment": "Sistem de plată", "systemPaymentDesc": "Una sau mai multe dintre plățile dvs. sunt o plată de sistem. ", "error": {"amountToLarge": "Nu se poate rambursa mai mult decât suma inițială a comenzii.", "amountNegative": "Suma rambursării trebuie să fie un număr pozitiv.", "reasonRequired": "Vă rugăm să selectați un motiv de rambursare."}}, "customer": {"contactLabel": "Contact", "editEmail": "Editați e-mailul", "transferOwnership": "Transferați proprietatea", "editBillingAddress": "Editați adresa de facturare", "editShippingAddress": "Editați adresa de livrare"}, "activity": {"header": "Activitate", "comment": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Lasă un comentariu", "addButtonText": "Adaugă comentariu", "deleteButtonText": "Șterge comentariul"}, "from": "<PERSON>", "to": "La", "events": {"common": {"toReturn": "Să se întoarcă", "toSend": "A trimite"}, "placed": {"title": "<PERSON>mand<PERSON> plasata", "fromSalesChannel": "din {{salesChannel}}"}, "canceled": {"title": "<PERSON><PERSON><PERSON>"}, "payment": {"awaiting": "În așteptarea plății", "captured": "Plata capturată", "canceled": "Plata anulată", "refunded": "Plata rambursată"}, "fulfillment": {"created": "Articole îndeplinite", "canceled": "Îndep<PERSON><PERSON> an<PERSON>", "shipped": "Articolele expediate", "delivered": "Articole livrate", "items_one": "{{count}} articol", "items_other": "{{count}} articole"}, "return": {"created": "Retur #{{returnId}} solicitat", "canceled": "Retur #{{returnId}} anulat", "received": "Retur #{{returnId}} primit", "items_one": "{{count}} articol returnat", "items_other": "{{count}} articole returnate"}, "note": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "byLine": "de {{author}}"}, "claim": {"created": "Revendicare #{{claimId}} solicitat", "canceled": "Revendicare #{{claimId}} anulat", "itemsInbound": "{{count}} articol de returnat", "itemsOutbound": "{{count}} articol de trimis"}, "exchange": {"created": "Schimb #{{exchangeId}} solicitat", "canceled": "Schimb #{{exchangeId}} anulat", "itemsInbound": "{{count}} articol de returnat", "itemsOutbound": "{{count}} articol de trimis"}, "edit": {"requested": "Modificarea comenzii #{{editId}} solicitat", "confirmed": "Modificarea comenzii #{{editId}} confirmat"}, "transfer": {"requested": "Transferul comenzii #{{transferId}} solicitat", "confirmed": "Transferul comenzii #{{transferId}} confirmat", "declined": "Transferul comenzii #{{transferId}} a refuzat"}, "update_order": {"shipping_address": "Adresa de livrare a fost actualizată", "billing_address": "Adresa de facturare a fost actualizată", "email": "E-mail actualizat"}}, "showMoreActivities_one": "Spectacol {{count}} mai multa activitate", "showMoreActivities_other": "Spectacol {{count}} mai multe activități"}, "fields": {"displayId": "ID afișat", "refundableAmount": "<PERSON>m<PERSON> rambur<PERSON>", "returnableQuantity": "Cantitate returnabilă"}}, "draftOrders": {"domain": "Proiecte de ordine", "deleteWarning": "Sunteți pe cale să ștergeți comanda nefinalizată {{id}}. ", "paymentLinkLabel": "Link de plată", "cartIdLabel": "ID coș", "markAsPaid": {"label": "Marcați ca plătit", "warningTitle": "Marcați ca plătit", "warningDescription": "Sunteți pe cale să marcați comanda nefinalizată ca plătită. "}, "status": {"open": "Deschide", "completed": "Terminat"}, "create": {"createDraftOrder": "Creați comanda nefinalizată", "createDraftOrderHint": "Creați o nouă comandă nefinalizată pentru a gestiona detaliile unei comenzi înainte ca aceasta să fie plasată.", "chooseRegionHint": "Alegeți regiunea", "existingItemsLabel": "Articole existente", "existingItemsHint": "Adăugați produse existente la comanda nefinalizată.", "customItemsLabel": "Articole personalizate", "customItemsHint": "Adăugați articole personalizate la comanda nefinalizată.", "addExistingItemsAction": "Adăugați articole existente", "addCustomItemAction": "Adăugați un articol personalizat", "noCustomItemsAddedLabel": "Nu s-au adăugat încă articole personalizate", "noExistingItemsAddedLabel": "Nu a fost adăugat încă niciun element existent", "chooseRegionTooltip": "Alegeți mai întâi o regiune", "useExistingCustomerLabel": "Utilizați clientul existent", "addShippingMethodsAction": "Adăugați metode de expediere", "unitPriceOverrideLabel": "Prețul unitar este anulat", "shippingOptionLabel": "Opțiune de livrare", "shippingOptionHint": "Alegeți opțiunea de livrare pentru comanda nefinalizată.", "shippingPriceOverrideLabel": "Prețul de livrare este anulat", "shippingPriceOverrideHint": "Înlocuiți prețul de livrare pentru comanda nefinalizată.", "sendNotificationLabel": "Trimite o notificare", "sendNotificationHint": "Trimiteți o notificare clientului când este creată comanda nefinalizată."}, "validation": {"requiredEmailOrCustomer": "E-mailul sau clientul este necesar.", "requiredItems": "Este necesar cel puțin un articol.", "invalidEmail": "E-mailul trebuie să fie o adresă de e-mail validă."}}, "stockLocations": {"domain": "Locații și transport", "list": {"description": "Gestionați locațiile de stoc ale magazinului dvs. și opțiunile de livrare."}, "create": {"header": "Creați locație stoc", "hint": "O locație de stoc este un site fizic de unde sunt depozitate și expediate produsele.", "successToast": "Locaţie {{name}} a fost creat cu succes."}, "edit": {"header": "Editați locația stocului", "viewInventory": "<PERSON><PERSON><PERSON><PERSON> inventarul", "successToast": "Locaţie {{name}} a fost actualizat cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți locația stocului {{name}}. "}, "fulfillmentProviders": {"header": "Furnizori de servicii", "shippingOptionsTooltip": "Acest meniu derulant va consta numai din furnizorii activați pentru această locație. ", "label": "Furnizori de servicii de onorare conectați", "connectedTo": "Conectat la {{count}} de {{total}} furnizorii de împlinire", "noProviders": "Această locație de stoc nu este conectată la niciun furnizor de servicii de onorare.", "action": "Conectați fur<PERSON>zorii", "successToast": "Furnizorii de servicii de onorare pentru locația stocului au fost actualizați cu succes."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "shipping": {"header": "Transport"}, "disable": {"confirmation": "Sunteți sigur că doriți să dezactivați {{name}}? ", "pickup": "Ridicarea a fost dezactivată cu succes.", "shipping": "Livrarea a fost dezactivată cu succes."}, "enable": {"pickup": "Ridicarea a fost activată cu succes.", "shipping": "Expedierea a fost activată cu succes."}}, "sidebar": {"header": "Configurație de livrare", "shippingProfiles": {"label": "<PERSON><PERSON><PERSON>pedier<PERSON>", "description": "Grupați produsele după cerințele de expediere"}}, "salesChannels": {"header": "Canale de vânzare", "label": "Canale de vânzare conectate", "connectedTo": "Conectat la {{count}} de {{total}} canale de vân<PERSON>e", "noChannels": "Locația nu este conectată la niciun canal de vânzare.", "action": "Conectați canalele de vânzare", "successToast": "Canalele de vânzare au fost actualizate cu succes."}, "shippingOptions": {"create": {"shipping": {"header": "Creați opțiunea de livrare pentru {{zone}}", "hint": "Creați o nouă opțiune de expediere pentru a defini modul în care produsele sunt expediate din această locație.", "label": "Opțiuni de livrare", "successToast": "Opțiune de livrare {{name}} a fost creat cu succes."}, "returns": {"header": "Creați o opțiune de returnare pentru {{zone}}", "hint": "Creați o nouă opțiune de returnare pentru a defini modul în care produsele sunt returnate în această locație.", "label": "Opțiuni de returnare", "successToast": "Opțiune de retur {{name}} a fost creat cu succes."}, "tabs": {"details": "<PERSON><PERSON><PERSON>", "prices": "Pre<PERSON><PERSON>"}, "action": "Creați opțiunea"}, "delete": {"confirmation": "Sunteți pe cale să ștergeți opțiunea de expediere {{name}}. ", "successToast": "Opțiune de livrare {{name}} a fost șters cu succes."}, "edit": {"header": "Editați opțiunea de livrare", "action": "Opțiune de editare", "successToast": "Opțiune de livrare {{name}} a fost actualizat cu succes."}, "pricing": {"action": "Editați preț<PERSON>"}, "conditionalPrices": {"header": "<PERSON><PERSON><PERSON> conditionate pt {{name}}", "description": "Gestionați prețurile condiționate pentru această opțiune de expediere în funcție de totalul articolului din coș.", "attributes": {"cartItemTotal": "Total articole din coș"}, "summaries": {"range": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> este între <1>{{gte}}</1> şi <2>{{lte}}</2>", "greaterThan": "Dacă <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Dacă <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "Adăugați preț", "manageConditionalPrices": "Gestionați prețurile condiționate"}, "rules": {"amount": "Prețul opțiunii de livrare", "gte": "Total minim de articole din coș", "lte": "Total maxim de articole din coș"}, "customRules": {"label": "Reguli personalizate", "tooltip": "Acest preț condiționat are reguli care nu pot fi gestionate în tabloul de bord.", "eq": "Totalul articolelor din coș trebuie să fie egal", "gt": "Totalul articolelor din coș trebuie să fie mai mare decât", "lt": "Totalul articolelor din coș trebuie să fie mai mic decât"}, "errors": {"amountRequired": "Prețul opțiunii de livrare este necesar", "minOrMaxRequired": "Trebuie furnizat cel puțin unul din totalul minim sau maxim al articolelor din coș", "minGreaterThanMax": "Totalul minim al articolelor din coș trebuie să fie mai mic sau egal cu totalul maxim al articolelor din coș", "duplicateAmount": "Prețul opțiunii de livrare trebuie să fie unic pentru fiecare condiție", "overlappingConditions": "Condițiile trebuie să fie unice în toate reguli<PERSON> de <PERSON>ț"}}, "fields": {"count": {"shipping_one": "{{count}} opțiunea de livrare", "shipping_other": "{{count}} opțiuni de expediere", "returns_one": "{{count}} op<PERSON><PERSON><PERSON> de <PERSON>are", "returns_other": "{{count}} opțiuni de returnare"}, "priceType": {"label": "Tip <PERSON>", "options": {"fixed": {"label": "Fix", "hint": "Prețul opțiunii de livrare este fix și nu se modifică în funcție de conținutul comenzii."}, "calculated": {"label": "Calculat", "hint": "Prețul opțiunii de livrare este calculat de furnizorul de servicii de onorare în timpul plății."}}}, "enableInStore": {"label": "Activați în magazin", "hint": "Dacă clienții pot folosi această opțiune în timpul plății."}, "provider": "Furnizor de îndeplinire", "profile": "<PERSON><PERSON> <PERSON> liv<PERSON>", "fulfillmentOption": "Opțiune de îndeplinire"}}, "serviceZones": {"create": {"headerPickup": "Creați o zonă de service pentru preluare de la {{location}}", "headerShipping": "Creați o zonă de servicii pentru expediere din {{location}}", "action": "Creați o zonă de serviciu", "successToast": "Zona de service {{name}} a fost creat cu succes."}, "edit": {"header": "Editați zona de servicii", "successToast": "Zona de service {{name}} a fost actualizat cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți zona de serviciu {{name}}. ", "successToast": "Zona de service {{name}} a fost șters cu succes."}, "manageAreas": {"header": "Gestionați zonele pentru {{name}}", "action": "Gestionați zonele", "label": "<PERSON>le", "hint": "Selectați zonele geografice pe care le acoperă zona de serviciu.", "successToast": "Zone pentru {{name}} au fost actualizate cu succes."}, "fields": {"noRecords": "Nu există zone de servicii pentru a adăuga opțiuni de expediere.", "tip": "O zonă de serviciu este o colecție de zone sau zone geografice. "}}}, "shippingProfile": {"domain": "<PERSON><PERSON><PERSON>pedier<PERSON>", "subtitle": "Grupați produse cu cerințe de expediere similare în profiluri.", "create": {"header": "Creați un profil de expediere", "hint": "Creați un nou profil de expediere pentru a grupa produse cu cerințe de expediere similare.", "successToast": "Profil de livrare {{name}} a fost creat cu succes."}, "delete": {"title": "Ștergeți profilul de expediere", "description": "Sunteți pe cale să ștergeți profilul de expediere {{name}}. ", "successToast": "Profil de livrare {{name}} a fost șters cu succes."}, "tooltip": {"type": "Introduceți tipul de profil de expediere, de exemplu: Greu, Supradimensionat, Numai pentru marfă etc."}}, "taxRegions": {"domain": "Regiuni fiscale", "list": {"hint": "Gestionați ceea ce percepeți clienții atunci când fac cumpărături din diferite țări și regiuni."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți o regiune fiscală. ", "successToast": "Regiunea fiscală a fost ștearsă cu succes."}, "create": {"header": "Creați regiune fiscală", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru o anumită țară.", "errors": {"rateIsRequired": "Cota de impozitare este necesară la crearea unei cote implicite de impozitare.", "nameIsRequired": "Numele este necesar la crearea unei rate de impozitare implicite."}, "successToast": "Regiunea fiscală a fost creată cu succes."}, "province": {"header": "Provin<PERSON><PERSON>", "create": {"header": "Creați regiunea fiscală provincială", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru o anumită provincie."}}, "state": {"header": "state", "create": {"header": "Creați regiunea fiscală de stat", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit stat."}}, "stateOrTerritory": {"header": "State sau Teritorii", "create": {"header": "Creați regiune fiscală de stat/teritoriu", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit stat/teritoriu."}}, "county": {"header": "judete", "create": {"header": "Creați regiunea fiscală județeană", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit județ."}}, "region": {"header": "Regiunile", "create": {"header": "Creați regiune fiscală regională", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru o anumită regiune."}}, "department": {"header": "Departamente", "create": {"header": "Creați regiunea fiscală departamentală", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit departament."}}, "territory": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Creați regiune fiscală teritorială", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit teritoriu."}}, "prefecture": {"header": "Prefecturi", "create": {"header": "Creați regiunea fiscală a prefecturii", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru o anumită prefectură."}}, "district": {"header": "Districte", "create": {"header": "Creați regiunea fiscală districtuală", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit district."}}, "governorate": {"header": "guvernorate", "create": {"header": "Creați regiunea fiscală a guvernatului", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru o anumită gubernie."}}, "canton": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Creați regiunea fiscală cantonană", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit canton."}}, "emirate": {"header": "Emiratele", "create": {"header": "Creați regiunea fiscală Emirate", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit emirat."}}, "sublevel": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Creați o regiune fiscală de subnivel", "hint": "Creați o nouă regiune fiscală pentru a defini cotele de impozitare pentru un anumit subnivel."}}, "taxOverrides": {"header": "Anulează", "create": {"header": "Creați suprascriere", "hint": "Creați o cotă de impozitare care înlocuiește cotele de impozitare implicite pentru condițiile selectate."}, "edit": {"header": "Editare suprascrie", "hint": "Editați cota de impozitare care înlocuiește cotele de impozitare implicite pentru condițiile selectate."}}, "taxRates": {"create": {"header": "Creați cota de impozitare", "hint": "Creați o nouă cotă de impozitare pentru a defini rata de impozitare pentru o regiune.", "successToast": "Cota de impozitare a fost creată cu succes."}, "edit": {"header": "Editați rata de impozitare", "hint": "Editați cota de impozitare pentru a defini rata de impozitare pentru o regiune.", "successToast": "Cota de impozitare a fost actualizată cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți cota de impozitare {{name}}. ", "successToast": "Cota de impozitare a fost ștearsă cu succes."}}, "fields": {"isCombinable": {"label": "Combinabil", "hint": "Dacă această cotă de impozitare poate fi combinată cu rata implicită din regiunea fiscală.", "true": "Combinabil", "false": "Nu se poate combina"}, "defaultTaxRate": {"label": "Cota de impozitare implicită", "tooltip": "Rata de impozitare implicită pentru această regiune. ", "action": "Creați o rată de impozitare implicită"}, "taxRate": "Cota de impozitare", "taxCode": "Cod fiscal", "targets": {"label": "Ținte", "hint": "Selectați obiectivele cărora li se va aplica această cotă de impozitare.", "options": {"product": "Produse", "productCollection": "Colecții de produse", "productTag": "Etichete de produs", "productType": "<PERSON><PERSON><PERSON> de produse", "customerGroup": "Grupuri <PERSON>"}, "operators": {"in": "în", "on": "pe", "and": "<PERSON>i"}, "placeholders": {"product": "Caut<PERSON> produse", "productCollection": "Căutați colecții de produse", "productTag": "Căutați etichete de produse", "productType": "Căutați tipuri de produse", "customerGroup": "Căutați grupuri de clienți"}, "tags": {"product": "Produs", "productCollection": "Colecția de produse", "productTag": "Eticheta produsului", "productType": "Tip de produs", "customerGroup": "Grup de clienți"}, "modal": {"header": "Adăugați ținte"}, "action": "Adăugați țintă", "values_one": "{{count}} valoare", "values_other": "{{count}} valorile", "numberOfTargets_one": "{{count}} <PERSON><PERSON><PERSON>", "numberOfTargets_other": "{{count}} tinte", "additionalValues_one": "şi {{count}} mai multă valoare", "additionalValues_other": "şi {{count}} mai multe valori"}, "sublevels": {"labels": {"province": "<PERSON><PERSON><PERSON>", "state": "Stat", "region": "Regiune", "stateOrTerritory": "Stat/Teritoriu", "department": "Departament", "county": "jud", "territory": "<PERSON><PERSON><PERSON><PERSON>", "prefecture": "Prefectura", "district": "District", "governorate": "Guvernoratul", "emirate": "Emirat", "canton": "Canton", "sublevel": "Cod de subnivel"}, "placeholders": {"province": "Selectați provincia", "state": "Selectați starea", "region": "Selectați regiunea", "stateOrTerritory": "Selectați statul/teritoriul", "department": "Selectați departamentul", "county": "Selectați județul", "territory": "Selectați teritoriul", "prefecture": "Selectați prefectura", "district": "Selectați districtul", "governorate": "Alegeți guvernoratul", "emirate": "Selectați emirate", "canton": "Selectați cantonul"}, "tooltips": {"sublevel": "Introduceți codul ISO 3166-2 pentru regiunea fiscală de subnivel.", "notPartOfCountry": "{{province}} nu pare să facă parte din {{country}}. "}, "alert": {"header": "Regiunile de subnivel sunt dezactivate pentru această regiune fiscală", "description": "Regiunile de subnivel sunt dezactivate pentru această regiune în mod implicit. ", "action": "Activați regiunile de subnivel"}}, "noDefaultRate": {"label": "Fără rată implicită", "tooltip": "Această regiune fiscală nu are o rată de impozitare implicită. "}}}, "promotions": {"domain": "Promoții", "sections": {"details": "Detalii de promovare"}, "tabs": {"template": "Tip", "details": "<PERSON><PERSON><PERSON>", "campaign": "<PERSON><PERSON><PERSON>"}, "fields": {"type": "Tip", "value_type": "Tip de valoare", "value": "Valoare", "campaign": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON>", "allocation": "Alocare", "addCondition": "Adăugați o condiție", "clearAll": "Ștergeți totul", "amount": {"tooltip": "Selectați codul monedei pentru a activa setarea sumei"}, "conditions": {"rules": {"title": "Cine poate folosi acest cod?", "description": "Care client are permisiunea de a folosi codul promoțional? "}, "target-rules": {"title": "La ce articole se va aplica promoția?", "description": "Promoția se va aplica articolelor care îndeplinesc următoarele condiții."}, "buy-rules": {"title": "Ce trebuie să fie în coș pentru a debloca promoția?", "description": "Dacă aceste condiții se potrivesc, activăm promovarea pe articolele țintă."}}}, "tooltips": {"campaignType": "Codul monedei trebuie selectat în promoție pentru a seta un buget de cheltuieli."}, "errors": {"requiredField": "Câmp obligatoriu", "promotionTabError": "Remediați erorile din fila Promoție înainte de a continua"}, "toasts": {"promotionCreateSuccess": "Promovare ({{code}}) a fost creat cu succes."}, "create": {}, "edit": {"title": "Editați detaliile promoției", "rules": {"title": "Editați condițiile de utilizare"}, "target-rules": {"title": "Editați condițiile articolului"}, "buy-rules": {"title": "Editați regulile de cumpărare"}}, "campaign": {"header": "<PERSON><PERSON><PERSON>", "edit": {"header": "Editați campania", "successToast": "Am actualizat cu succes campania promoției."}, "actions": {"goToCampaign": "<PERSON><PERSON><PERSON> la campanie"}}, "campaign_currency": {"tooltip": "Aceasta este moneda promoției. "}, "form": {"required": "<PERSON><PERSON><PERSON><PERSON>", "and": "ŞI", "selectAttribute": "Selectați Atribut", "campaign": {"existing": {"title": "Campanie existentă", "description": "Adăugați promovare la o campanie existentă.", "placeholder": {"title": "Nu există campanii existente", "desc": "Puteți crea unul pentru a urmări mai multe promoții și pentru a stabili limite de buget."}}, "new": {"title": "Noua campanie", "description": "Creați o nouă campanie pentru această promoție."}, "none": {"title": "<PERSON><PERSON><PERSON><PERSON> campanie", "description": "Continuați fără a asocia promovarea cu campania"}}, "status": {"label": "Status", "draft": {"title": "Draft", "description": "Clienții nu vor putea folosi încă codul"}, "active": {"title": "Activ", "description": "Clienț<PERSON> vor putea folosi codul"}, "inactive": {"title": "Inactiv", "description": "Clienții nu vor mai putea folosi codul"}}, "method": {"label": "<PERSON><PERSON><PERSON>", "code": {"title": "Cod promoțional", "description": "Clienții trebuie să introducă acest cod la finalizarea comenzii"}, "automatic": {"title": "Automat", "description": "Clienții vor vedea această promoție la finalizarea comenzii"}}, "max_quantity": {"title": "Cantitate maxima", "description": "Cantitatea maximă de articole pentru care se aplică această promoție."}, "type": {"standard": {"title": "Standard", "description": "O promovare standard"}, "buyget": {"title": "Cumpărați Obțineți", "description": "Cumpărați X obțineți promoția Y"}}, "allocation": {"each": {"title": "<PERSON><PERSON><PERSON>", "description": "Aplică valoare fiecărui articol"}, "across": {"title": "Peste tot", "description": "Aplică valoarea articolelor"}}, "code": {"title": "Cod", "description": "Codul pe care clienții dumneavoastră îl vor introduce în timpul plății."}, "value": {"title": "Valoarea de promovare"}, "value_type": {"fixed": {"title": "Valoarea de promovare", "description": "Suma care trebuie redusă. "}, "percentage": {"title": "Valoarea de promovare", "description": "Procentul de reducere a sumei. "}}}, "deleteWarning": "Sunteți pe cale să ștergeți promoția {{code}}. ", "createPromotionTitle": "Creați o promoție", "type": "<PERSON>ip de promovare", "conditions": {"add": "Adăugați o condiție", "list": {"noRecordsMessage": "Adăugați o condiție pentru a restricționa articolele cărora li se aplică promoția."}}}, "campaigns": {"domain": "Campanii", "details": "Detalii campanie", "status": {"active": "Activ", "expired": "Expirat", "scheduled": "Programat"}, "delete": {"title": "esti sigur?", "description": "Sunteți pe cale să ștergeți campania \"{{name}}'. ", "successToast": "Campania '{{name}}' a fost creat cu succes."}, "edit": {"header": "Editați campania", "description": "Editați detaliile campaniei.", "successToast": "Campania '{{name}}' a fost actualizat cu succes."}, "configuration": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Editați configurația campaniei", "description": "Editați configurația campaniei.", "successToast": "Configurația campaniei a fost actualizată cu succes."}}, "create": {"title": "Creați o campanie", "description": "Creați o campanie promoțională.", "hint": "Creați o campanie promoțională.", "header": "Creați o campanie", "successToast": "Campania '{{name}}' a fost creat cu succes."}, "fields": {"name": "Nume", "identifier": "Identificator", "start_date": "Data de începere", "end_date": "Data de încheiere", "total_spend": "<PERSON><PERSON><PERSON> cheltuit", "total_used": "Bugetul folosit", "budget_limit": "Limită <PERSON>", "campaign_id": {"hint": "În această listă sunt afișate numai campaniile cu același cod de monedă ca și promoția."}}, "budget": {"create": {"hint": "Creați un buget pentru campanie.", "header": "Bugetul campaniei"}, "details": "Bugetul campaniei", "fields": {"type": "Tip", "currency": "Valută", "limit": "Limită", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "<PERSON><PERSON><PERSON>", "description": "Stabiliți o limită a sumei totale reduse pentru toate utilizările promoției."}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Stabiliți o limită de câte ori poate fi utilizată promoția."}}, "edit": {"header": "Editați bugetul campaniei"}}, "promotions": {"remove": {"title": "Eliminați promovarea din campanie", "description": "Sunteți pe cale să eliminați {{count}} promoție(e) din campanie. "}, "alreadyAdded": "Această promoție a fost deja adăugată în campanie.", "alreadyAddedDiffCampaign": "Această promoție a fost deja adăugată la o altă campanie ({{name}}).", "currencyMismatch": "Moneda promoției și a campaniei nu se potrivește", "toast": {"success": "Adăugat cu succes {{count}} promovare(e) la campanie"}, "add": {"list": {"noRecordsMessage": "Creați mai întâi o promoție."}}, "list": {"noRecordsMessage": "Nu există promoții în campanie."}}, "deleteCampaignWarning": "Sunteți pe cale să ștergeți campania {{name}}. ", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Liste de prețuri", "subtitle": "Creați prețuri de vânzare sau înlocuiți pentru anumite condiții.", "delete": {"confirmation": "Sunteți pe cale să ștergeți lista de prețuri {{title}}. ", "successToast": "Lista de preturi {{title}} a fost șters cu succes."}, "create": {"header": "Creați lista de prețuri", "subheader": "Creați o nouă listă de prețuri pentru a gestiona prețurile produselor dvs.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "products": "Produse", "prices": "Pre<PERSON><PERSON>"}, "successToast": "Lista de preturi {{title}} a fost creat cu succes.", "products": {"list": {"noRecordsMessage": "Creați mai întâi un produs."}}}, "edit": {"header": "Editați lista de prețuri", "successToast": "Lista de preturi {{title}} a fost actualizat cu succes."}, "configuration": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Editați configurația listei de prețuri", "description": "Editați configurația listei de prețuri.", "successToast": "Configurația listei de prețuri a fost actualizată cu succes."}}, "products": {"header": "Produse", "actions": {"addProducts": "Adă<PERSON><PERSON><PERSON> produse", "editPrices": "Editați preț<PERSON>"}, "delete": {"confirmation_one": "Sunteți pe cale să ștergeți prețurile pentru {{count}} produs în lista de prețuri. ", "confirmation_other": "Sunteți pe cale să ștergeți prețurile pentru {{count}} produsele din lista de preturi. ", "successToast_one": "<PERSON><PERSON><PERSON> cu succes pentru {{count}} produs.", "successToast_other": "<PERSON><PERSON><PERSON> cu succes pentru {{count}} produse."}, "add": {"successToast": "Prețurile au fost adăugate cu succes la lista de prețuri."}, "edit": {"successToast": "Prețurile au fost actualizate cu succes."}}, "fields": {"priceOverrides": {"label": "Prețul depășește", "header": "În<PERSON><PERSON><PERSON>"}, "status": {"label": "Stare", "options": {"active": "Activ", "draft": "Proiect", "expired": "Expirat", "scheduled": "Programat"}}, "type": {"label": "Tip", "hint": "Alegeți tipul de listă de prețuri pe care doriți să o creați.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ț<PERSON><PERSON> de vânzare sunt modificări temporare de preț pentru produse."}, "override": {"label": "Suprasc<PERSON><PERSON>", "description": "Înlocuirile sunt de obicei folosite pentru a crea prețuri specifice clientului."}}}, "startsAt": {"label": "Lista de prețuri are o dată de începere?", "hint": "Programați lista de prețuri pentru a o activa în viitor."}, "endsAt": {"label": "Lista de prețuri are o dată de expirare?", "hint": "Programați lista de prețuri pentru a o dezactiva pe viitor."}, "customerAvailability": {"header": "Alegeți grupuri de clienți", "label": "Disponibilitatea clientului", "hint": "Alegeți grupurile de clienți cărora li se va aplica lista de prețuri.", "placeholder": "Căutați grupuri de clienți", "attribute": "Grupuri <PERSON>"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Gestionați detaliile profilului dvs.", "fields": {"languageLabel": "Limbă", "usageInsightsLabel": "Informații despre utilizare"}, "edit": {"header": "Editați profilul", "languageHint": "Limba pe care doriți să o utilizați în tabloul de bord administrativ. ", "languagePlaceholder": "Selectați limba", "usageInsightsHint": "Distribuiți informații despre utilizare și ajutați-ne să îmbunătățim Medusa.  <0>documentare</0>."}, "toast": {"edit": "Modificările profilurilor au fost salvate"}}, "users": {"domain": "Util<PERSON><PERSON><PERSON>", "editUser": "Editați utilizatorul", "inviteUser": "Invitați utilizator", "inviteUserHint": "Invitați un utilizator nou în magazinul dvs.", "sendInvite": "Trimite invitația", "pendingInvites": "Invitații în așteptare", "deleteInviteWarning": "Sunteți pe cale să ștergeți invitația pentru {{email}}. ", "resendInvite": "Retrimiteți invitația", "copyInviteLink": "Copiați linkul de invitație", "expiredOnDate": "A expirat pe {{date}}", "validFromUntil": "Valabil de la <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Acceptat pe {{date}}", "inviteStatus": {"accepted": "Acceptat", "pending": "În așteptare", "expired": "Expirat"}, "roles": {"admin": "Admin", "developer": "Dezvoltator", "member": "Membru"}, "list": {"empty": {"heading": "Nu s-au găsit utilizatori", "description": "Odată ce un utilizator a fost invitat, acesta va apărea aici."}, "filtered": {"heading": "<PERSON><PERSON><PERSON> rezultat", "description": "Niciun utilizator nu corespunde criteriilor actuale de filtrare."}}, "deleteUserWarning": "Sunteți pe cale să ștergeți utilizatorul {{name}}. ", "deleteUserSuccess": "Utilizatorul {{name}} a fost șters cu succes", "invite": "Invi<PERSON>"}, "store": {"domain": "Magazin", "manageYourStoresDetails": "Gestionați detaliile magazinului dvs", "editStore": "Editează magazinul", "defaultCurrency": "Moneda implicită", "defaultRegion": "Regiunea implicită", "swapLinkTemplate": "Schimbați șablon de link", "paymentLinkTemplate": "Șablon de link de plată", "inviteLinkTemplate": "Șablon de link de invitație", "currencies": "<PERSON><PERSON>", "addCurrencies": "Adăugați valute", "enableTaxInclusivePricing": "Activați prețul cu taxe incluse", "disableTaxInclusivePricing": "Dezactivați prețul cu taxe incluse", "currencyAlreadyAdded": "Moneda a fost deja adăugată în magazinul dvs.", "edit": {"header": "Editați magazinul"}, "toast": {"update": "Magazinul a fost actualizat cu succes", "currenciesUpdated": "Monede au fost actualizate cu succes", "currenciesRemoved": "S-au eliminat cu succes monedele din magazin", "updatedTaxInclusivitySuccessfully": "Prețul cu taxe incluse a fost actualizat cu succes"}, "removeCurrencyWarning_one": "Sunteți pe cale să eliminați {{count}} moneda din magazinul dvs. ", "removeCurrencyWarning_other": "Sunteți pe cale să eliminați {{count}} monede din magazinul dvs. "}, "regions": {"domain": "Regiunile", "subtitle": "O regiune este o zonă în care vindeți produse. Poate acoperi mai multe țări și are cote de impozitare, furnizori și monede diferite.", "createRegion": "<PERSON><PERSON><PERSON><PERSON> regiune", "createRegionHint": "Gestionați ratele de impozitare și furnizorii pentru un set de țări.", "addCountries": "Adăugați țări", "editRegion": "Editați regiunea", "countriesHint": "Adăugați țările incluse în această regiune.", "deleteRegionWarning": "Sunteți pe cale să ștergeți regiunea {{name}}. ", "removeCountryWarning": "Sunteți pe cale să eliminați țara {{name}} din regiune. ", "automaticTaxesHint": "Când este activată, taxele vor fi calculate numai la finalizarea comenzii pe baza adresei de expediere.", "taxInclusiveHint": "Când este activat, prețurile din regiune vor fi cu taxe incluse.", "providersHint": " Adăugați ce furnizori de plăți sunt disponibili în această regiune.", "shippingOptions": "Opțiuni de livrare", "deleteShippingOptionWarning": "Sunteți pe cale să ștergeți opțiunea de expediere {{name}}. ", "return": "<PERSON><PERSON>", "outbound": "Ieșire", "priceType": "Tip <PERSON>", "flatRate": "Preţ global", "calculated": "Calculat", "list": {"noRecordsMessage": "Creați o regiune pentru zonele în care vindeți."}, "toast": {"delete": "Regiunea a fost ștearsă cu succes", "edit": "Editarea regiunii a fost salvată", "create": "Regiunea creată cu succes", "countries": "Țările din regiune au fost actualizate cu succes"}, "shippingOption": {"createShippingOption": "Creați opțiunea de livrare", "createShippingOptionHint": "Creați o nouă opțiune de expediere pentru regiune.", "editShippingOption": "Editați opțiunea de livrare", "fulfillmentMethod": "Metoda de împlinire", "type": {"outbound": "Ieșire", "outboundHint": "Utilizați acest lucru dacă creați o opțiune de expediere pentru trimiterea produselor către client.", "return": "<PERSON><PERSON>", "returnHint": "Utilizați acest lucru dacă creați o opțiune de expediere pentru ca clientul să vă returneze produsele."}, "priceType": {"label": "Tip <PERSON>", "flatRate": "Preţ global", "calculated": "Calculat"}, "availability": {"adminOnly": "Doar administrator", "adminOnlyHint": "Când este activată, opțiunea de livrare va fi disponibilă numai în tabloul de bord administrativ și nu în vitrina magazinului."}, "taxInclusiveHint": "Când este activată, prețul opțiunii de expediere va fi cu taxe incluse.", "requirements": {"label": "Cerințe", "hint": "Specificați cerințele pentru opțiunea de expediere."}}, "removeCountriesWarning_one": "Sunteți pe cale să eliminați {{count}} tara din regiune. ", "removeCountriesWarning_other": "Sunteți pe cale să eliminați {{count}} ț<PERSON><PERSON> din regiune. "}, "taxes": {"domain": "Regiuni fiscale", "domainDescription": "Gestionați-vă regiunea fiscală", "countries": {"taxCountriesHint": "Setările fiscale se aplică țărilor enumerate."}, "settings": {"editTaxSettings": "Editați setările fiscale", "taxProviderLabel": "Furnizor de taxe", "systemTaxProviderLabel": "Furnizor de taxe de sistem", "calculateTaxesAutomaticallyLabel": "Calculați automat taxele", "calculateTaxesAutomaticallyHint": "Când este activată, cotele de impozitare vor fi calculate automat și aplicate cărucioarelor. ", "applyTaxesOnGiftCardsLabel": "Aplicați taxe pe cardurile cadou", "applyTaxesOnGiftCardsHint": "Când este activată, taxele vor fi aplicate cardurilor cadou la finalizare. ", "defaultTaxRateLabel": "Cota de impozitare implicită", "defaultTaxCodeLabel": "Cod fiscal implicit"}, "defaultRate": {"sectionTitle": "Rata de impozitare implicită"}, "taxRate": {"sectionTitle": "Ratele de impozitare", "createTaxRate": "Creați cota de impozitare", "createTaxRateHint": "Creați o nouă cotă de impozitare pentru regiune.", "deleteRateDescription": "Sunteți pe cale să ștergeți cota de impozitare {{name}}. ", "editTaxRate": "Editați rata de impozitare", "editRateAction": "Editați rata", "editOverridesAction": "Modificați suprascrieri", "editOverridesTitle": "Editați anulări ale cotei de impozitare", "editOverridesHint": "Specificați suprascrierile pentru rata de impozitare.", "deleteTaxRateWarning": "Sunteți pe cale să ștergeți cota de impozitare {{name}}. ", "productOverridesLabel": "<PERSON><PERSON><PERSON><PERSON> de pro<PERSON>", "productOverridesHint": "Specificați suprascrierile de produs pentru rata de impozitare.", "addProductOverridesAction": "Adăugați înlocuiri de produs", "productTypeOverridesLabel": "Tipul de produs înlocuieș<PERSON>", "productTypeOverridesHint": "Specificați suprascrierile tipului de produs pentru rata de impozitare.", "addProductTypeOverridesAction": "Adăugați înlocuiri de tip de produs", "shippingOptionOverridesLabel": "Opțiunea de livrare anulează", "shippingOptionOverridesHint": "Specificați opțiunile de expediere pentru anularea cotei de impozitare.", "addShippingOptionOverridesAction": "Adăugați opțiuni de expediere", "productOverridesHeader": "Produse", "productTypeOverridesHeader": "<PERSON><PERSON><PERSON> de produse", "shippingOptionOverridesHeader": "Opțiuni de livrare"}}, "locations": {"domain": "Locații", "editLocation": "Editați locația", "addSalesChannels": "Adăugați canale de vânzare", "noLocationsFound": "Nu au fost găsite locații", "selectLocations": "Selectați locațiile care stochează articolul.", "deleteLocationWarning": "Sunteți pe cale să ștergeți locația {{name}}. ", "toast": {"create": "Locație creată cu succes", "update": "Locația a fost actualizată cu succes", "removeChannel": "Canalul de vânzări a fost eliminat cu succes"}, "removeSalesChannelsWarning_one": "Sunteți pe cale să eliminați {{count}} canal de vânzare din locație.", "removeSalesChannelsWarning_other": "Sunteți pe cale să eliminați {{count}} canale de vânzare din locație."}, "reservations": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Gestionați cantitatea rezervată de articole de inventar.", "deleteWarning": "Sunteți pe cale să ștergeți o rezervare. "}, "salesChannels": {"domain": "Canale de vânzare", "subtitle": "Gestionați canalele online și offline pe care vindeți produse.", "createSalesChannel": "Creați un canal de vânzări", "createSalesChannelHint": "Creați un nou canal de vânzare pentru a vă vinde produsele.", "enabledHint": "Specificați dacă canalul de vânzări este activat.", "addProducts": "Adă<PERSON><PERSON><PERSON> produse", "editSalesChannel": "Editați canalul de vânzări", "productAlreadyAdded": "Produsul a fost deja adăugat la canalul de vânzări.", "deleteSalesChannelWarning": "Sunteți pe cale să ștergeți canalul de vânzări {{name}}. ", "toast": {"create": "Canal de vânzări creat cu succes", "update": "Canalul de vânzări a fost actualizat cu succes", "delete": "Canalul de vânzări a fost șters cu succes"}, "tooltip": {"cannotDeleteDefault": "Canalul de vânzări implicit nu poate fi șters"}, "products": {"list": {"noRecordsMessage": "Nu există produse în canalul de vânzare."}, "add": {"list": {"noRecordsMessage": "Creați mai întâi un produs."}}}, "removeProductsWarning_one": "Sunteți pe cale să eliminați {{count}} produs de la {{sales_channel}}.", "removeProductsWarning_other": "Sunteți pe cale să eliminați {{count}} produse de la {{sales_channel}}."}, "apiKeyManagement": {"domain": {"publishable": "Chei API care pot fi publicate", "secret": "Chei secrete API"}, "subtitle": {"publishable": "Gestionați cheile API utilizate în vitrina pentru a limita sfera solicitărilor la anumite canale de vânzare.", "secret": "Gestionați cheile API utilizate pentru autentificarea utilizatorilor admin în aplicațiile de administrare."}, "status": {"active": "Activ", "revoked": "Revocat"}, "type": {"publishable": "Publicabil", "secret": "Secret"}, "create": {"createPublishableHeader": "Creați cheia API publicabilă", "createPublishableHint": "Creați o nouă cheie API publicabilă pentru a limita sfera solicitărilor la anumite canale de vânzare.", "createSecretHeader": "Creați cheia API secretă", "createSecretHint": "Creați o nouă cheie API secretă pentru a accesa API-ul Medusa ca utilizator administrator autentificat.", "secretKeyCreatedHeader": "Cheie secretă creată", "secretKeyCreatedHint": "Noua ta cheie secretă a fost generată. ", "copySecretTokenSuccess": "Cheia secretă a fost copiată în clipboard.", "copySecretTokenFailure": "Nu s-a putut copia cheia secretă în clipboard.", "successToast": "Cheia API a fost creată cu succes."}, "edit": {"header": "Editați cheia API", "description": "Editați titlul cheii API.", "successToast": "cheie API {{title}} a fost actualizat cu succes."}, "salesChannels": {"title": "Adăugați canale de vânzări", "description": "Adăugați canalele de vânzare la care cheia API ar trebui să fie limitată.", "alreadyAddedTooltip": "Canalul de vânzări a fost deja adăugat la cheia API.", "list": {"noRecordsMessage": "Nu există canale de vânzare în domeniul de aplicare al cheii API publicabile."}, "successToast_one": "{{count}} canalul de vânzări a fost adăugat cu succes la cheia API.", "successToast_other": "{{count}} canalele de vânzare au fost adăugate cu succes la cheia API."}, "delete": {"warning": "Sunteți pe cale să ștergeți cheia API {{title}}. ", "successToast": "cheie API {{title}} a fost șters cu succes."}, "revoke": {"warning": "Sunteți pe cale să revocați cheia API {{title}}. ", "successToast": "cheie API {{title}} a fost revocat cu succes."}, "addSalesChannels": {"list": {"noRecordsMessage": "Creați mai întâi un canal de vânzări."}}, "removeSalesChannel": {"warning": "Sunteți pe cale să eliminați canalul de vânzări {{name}} din cheia API. ", "successToast": "Canalul de vânzări a fost eliminat cu succes din cheia API.", "warningBatch_one": "Sunteți pe cale să eliminați {{count}} canal de vânzări din cheia API. ", "warningBatch_other": "Sunteți pe cale să eliminați {{count}} canale de vânzare din cheia API. ", "successToastBatch_one": "{{count}} canalul de vânzări a fost eliminat cu succes din cheia API.", "successToastBatch_other": "{{count}} canalele de vânzare au fost eliminate cu succes din cheia API."}, "actions": {"revoke": "Revocați cheia API", "copy": "Copiați cheia API", "copySuccessToast": "Cheia API a fost copiată în clipboard."}, "table": {"lastUsedAtHeader": "Ultima utilizare la", "createdAtHeader": "Revocat la"}, "fields": {"lastUsedAtLabel": "Ultima utilizare la", "revokedByLabel": "Revocat de", "revokedAtLabel": "Revocat la", "createdByLabel": "<PERSON><PERSON><PERSON> de"}}, "returnReasons": {"domain": "Motive de întoarcere", "subtitle": "Gestionați motivele pentru articolele returnate.", "calloutHint": "Gestionați motivele pentru clasificarea returnărilor.", "editReason": "Editați motivul returnării", "create": {"header": "Adăugați motivul returnării", "subtitle": "Specificați cele mai frecvente motive pentru returnări.", "hint": "Creați un nou motiv de returnare pentru a clasifica returnările.", "successToast": "Motivul returnării {{label}} a fost creat cu succes."}, "edit": {"header": "Editați motivul returnării", "subtitle": "Editați valoarea motivului returnării.", "successToast": "Motivul returnării {{label}} a fost actualizat cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți motivul returnării {{label}}. ", "successToast": "Motivul returnării {{label}} a fost șters cu succes."}, "fields": {"value": {"label": "Valoare", "placeholder": "dimensiune_ greșită", "tooltip": "Valoarea ar trebui să fie un identificator unic pentru motivul returnării."}, "label": {"label": "Etiche<PERSON>", "placeholder": "Dimensiune greșită"}, "description": {"label": "Des<PERSON><PERSON><PERSON>", "placeholder": "Clientul a primit o dimensiune greșită"}}}, "login": {"forgotPassword": "Aţi uitat parola?  <0>Resetați</0>", "title": "Bun venit la Medusa", "hint": "Conectați-vă pentru a accesa zona de cont"}, "invite": {"title": "Bun venit la Medusa", "hint": "Creează-ți contul mai jos", "backToLogin": "Înapoi la autentificare", "createAccount": "Creează cont", "alreadyHaveAccount": "Ave<PERSON>i deja un cont?  <0>Log in</0>", "emailTooltip": "E-mailul dvs. nu poate fi schimbat. ", "invalidInvite": "Invitația este nevalidă sau a expirat.", "successTitle": "Contul dvs. a fost înregistrat", "successHint": "Începeți imediat cu Medusa Admin.", "successAction": "Porniți Medusa Admin", "invalidTokenTitle": "Indicatorul dvs. de invitație este nevalid", "invalidTokenHint": "Încercați să solicitați un nou link de invitație.", "passwordMismatch": "Parolele nu se potrivesc", "toast": {"accepted": "Invitația a fost acceptată"}}, "resetPassword": {"title": "Resetează parola", "hint": "Introduceți adresa dvs. de e-mail mai jos și vă vom trimite instrucțiuni despre cum să vă resetați parola.", "email": "E-mail", "sendResetInstructions": "Trimiteți instrucțiuni de resetare", "backToLogin": "<0>Înapoi la autentificare</0>", "newPasswordHint": "Alegeți o nouă parolă de mai jos.", "invalidTokenTitle": "Indicatorul dvs. de resetare este nevalid", "invalidTokenHint": "Încercați să solicitați un nou link de resetare.", "expiredTokenTitle": "Indicatorul dvs. de resetare a expirat", "goToResetPassword": "Accesați Resetare parolă", "resetPassword": "Resetează parola", "newPassword": "<PERSON><PERSON><PERSON>", "repeatNewPassword": "Repetați parola nouă", "tokenExpiresIn": "Tokenul expiră în <0>{{time}}</0> minute", "successfulRequestTitle": "V-a trimis cu succes un e-mail", "successfulRequest": "V-am trimis un e-mail pe care îl puteți folosi pentru a vă reseta parola. ", "successfulResetTitle": "Resetarea parolei cu succes", "successfulReset": "Vă rugăm să vă conectați pe pagina de conectare.", "passwordMismatch": "Parolele nu se potrivesc", "invalidLinkTitle": "Linkul dvs. de resetare este nevalid", "invalidLinkHint": "Încercați să vă resetați parola din nou."}, "workflowExecutions": {"domain": "Fluxuri de lucru", "subtitle": "Vizualizați și urmăriți execuțiile fluxului de lucru în aplicația dvs. Medusa.", "transactionIdLabel": "ID tranzacție", "workflowIdLabel": "ID flux de lucru", "progressLabel": "Progres", "list": {"noRecordsMessage": "Încă nu au fost executate fluxuri de lucru."}, "history": {"sectionTitle": "<PERSON><PERSON><PERSON>", "runningState": "Funcţionare...", "awaitingState": "În așteptare", "failedState": "A eșuat", "skippedState": "<PERSON><PERSON><PERSON>", "skippedFailureState": "O<PERSON> (Eșec)", "definitionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputLabel": "Ieșire", "compensateInputLabel": "Compensați intrarea", "revertedLabel": "<PERSON><PERSON><PERSON>", "errorLabel": "Eroare"}, "state": {"done": "Făcut", "failed": "A eșuat", "reverted": "<PERSON><PERSON><PERSON>", "invoking": "Invocarea", "compensating": "Compensarea", "notStarted": "Nu a început"}, "transaction": {"state": {"waitingToCompensate": "Așteptând compensarea"}}, "step": {"state": {"skipped": "<PERSON><PERSON><PERSON>", "skippedFailure": "O<PERSON> (Eșec)", "dormant": "Adormit", "timeout": "Pauză"}}, "stepsCompletedLabel_one": "{{completed}} de {{count}} pas", "stepsCompletedLabel_other": "{{completed}} de {{count}} trepte"}, "productTypes": {"domain": "<PERSON><PERSON><PERSON> de produse", "subtitle": "Organizați-vă produsele pe tipuri.", "create": {"header": "Creați tip de produs", "hint": "Creați un nou tip de produs pentru a vă clasifica produsele.", "successToast": "Tip de produs {{value}} a fost creat cu succes."}, "edit": {"header": "Editați tipul de produs", "successToast": "Tip de produs {{value}} a fost actualizat cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți tipul de produs {{value}}. ", "successToast": "Tip de produs {{value}} a fost șters cu succes."}, "fields": {"value": "Valoare"}}, "productTags": {"domain": "Etichete de produs", "create": {"header": "Creați etichetă de produs", "subtitle": "Creați o nouă etichetă de produs pentru a vă clasifica produsele.", "successToast": "Eticheta produsului {{value}} a fost creat cu succes."}, "edit": {"header": "Editați eticheta produsului", "subtitle": "Editați valoarea etichetei produsului.", "successToast": "Eticheta produsului {{value}} a fost actualizat cu succes."}, "delete": {"confirmation": "Sunteți pe cale să ștergeți eticheta produsului {{value}}. ", "successToast": "Eticheta produsului {{value}} a fost șters cu succes."}, "fields": {"value": "Valoare"}}, "notifications": {"domain": "Notific<PERSON><PERSON>", "emptyState": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>u aveți notific<PERSON> moment<PERSON>, dar odată ce o faceți vor locui aici."}, "accessibility": {"description": "notificările despre activitățile Medusa vor fi listate aici."}}, "errors": {"serverError": "Eroare de server - Încercați din nou mai târziu.", "invalidCredentials": "E-mail sau parolă greșită"}, "statuses": {"scheduled": "Programat", "expired": "Expirat", "active": "Activ", "inactive": "Inactiv", "draft": "Draft", "enabled": "Activat", "disabled": "Dezactivat"}, "labels": {"productVariant": "V<PERSON><PERSON> de produs", "prices": "Pre<PERSON><PERSON>", "available": "Disponibil", "inStock": "În stoc", "added": "Adă<PERSON><PERSON>", "removed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "<PERSON>", "to": "La"}, "fields": {"amount": "Cantitate", "refundAmount": "<PERSON><PERSON>", "name": "Nume", "default": "Implicit", "lastName": "Nume", "firstName": "Prenume", "title": "Titlu", "customTitle": "Titlu personalizat", "manageInventory": "Gestionați inventarul", "inventoryKit": "Are kit de inventar", "inventoryItems": "Articole de inventar", "inventoryItem": "Articol de inventar", "requiredQuantity": "Cantitatea necesară", "description": "Des<PERSON><PERSON><PERSON>", "email": "E-mail", "password": "Pa<PERSON><PERSON>", "repeatPassword": "Repetați parola", "confirmPassword": "Confirma<PERSON><PERSON>", "newPassword": "<PERSON><PERSON><PERSON>", "repeatNewPassword": "Repetați parola nouă", "categories": "Categorii", "shippingMethod": "Metoda de livrare", "configurations": "Configurații", "conditions": "Condiții", "category": "Categorie", "collection": "Colectare", "discountable": "Reducere", "handle": "<PERSON><PERSON><PERSON>", "subtitle": "Subtitlu", "by": "De", "item": "Articol", "qty": "cantitate.", "limit": "Limită", "tags": "Etichete", "type": "Tip", "reason": "Motiv", "none": "nici unul", "all": "toate", "search": "<PERSON><PERSON><PERSON><PERSON>", "percentage": "Procent", "sales_channels": "Canale de vânzare", "customer_groups": "Grupuri <PERSON>", "product_tags": "Etichete de produs", "product_types": "<PERSON><PERSON><PERSON> de produse", "product_collections": "Colecții de produse", "status": "Stare", "code": "Cod", "value": "Valoare", "disabled": "Dezactivat", "dynamic": "Dinamic", "normal": "Normal", "years": "ani", "months": "<PERSON><PERSON>", "days": "Zile", "hours": "Ore", "minutes": "Minute", "totalRedemptions": "Răscumpă<PERSON><PERSON><PERSON> totale", "countries": "<PERSON><PERSON><PERSON>", "paymentProviders": "Furnizorii de plăți", "refundReason": "<PERSON><PERSON><PERSON><PERSON> rambur<PERSON><PERSON><PERSON>i", "fulfillmentProviders": "Furnizori de servicii", "fulfillmentProvider": "Furnizor de servicii", "providers": "Furnizorii", "availability": "Disponibilitate", "inventory": "Inventar", "optional": "Opțional", "note": "<PERSON>a", "automaticTaxes": "Taxe automate", "taxInclusivePricing": "Prețuri cu taxe incluse", "currency": "Valută", "address": "<PERSON><PERSON><PERSON>", "address2": "Apartament, apartament etc.", "city": "Oraş", "postalCode": "Cod poștal", "country": "Ţară", "state": "Stat", "province": "<PERSON><PERSON><PERSON>", "company": "Companie", "phone": "Telefon", "metadata": "Metadate", "selectCountry": "Selectați țara", "products": "Produse", "variants": "<PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON>", "total": "Total comanda", "paidTotal": "Total capturat", "totalExclTax": "Total excl. ", "subtotal": "Subtotal", "shipping": "Transport", "outboundShipping": "Livrare i<PERSON>șire", "returnShipping": "Transport de retur", "tax": "Taxa", "created": "<PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "customer": "Client", "date": "Data", "order": "<PERSON><PERSON><PERSON>", "fulfillment": "Îndeplinire", "provider": "Furnizor", "payment": "<PERSON><PERSON><PERSON>", "items": "Articole", "salesChannel": "Canal de vânzări", "region": "Regiune", "discount": "Reducere", "role": "Rol", "sent": "<PERSON><PERSON>", "salesChannels": "Canale de vânzare", "product": "Produs", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Actualizat", "revokedAt": "Revocat la", "true": "<PERSON><PERSON><PERSON><PERSON>", "false": "Fals", "giftCard": "Card cadou", "tag": "Etichetă", "dateIssued": "Data emiterii", "issuedDate": "Data emiterii", "expiryDate": "Data de expirare", "price": "Preţ", "priceTemplate": "Preţ {{regionOrCurrency}}", "height": "Înălţime", "width": "Lăţime", "length": "Lungime", "weight": "Greutate", "midCode": "Codul MID", "hsCode": "Cod HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Cantitatea de inventar", "barcode": "Cod de bare", "countryOfOrigin": "Țara de origine", "material": "Material", "thumbnail": "Miniatură", "sku": "SKU", "managedInventory": "Inventar gestionat", "allowBackorder": "Permiteți comanda în așteptare", "inStock": "În stoc", "location": "Locaţie", "quantity": "Cantitate", "variant": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON><PERSON>", "minSubtotal": "<PERSON>. ", "maxSubtotal": "<PERSON>. ", "shippingProfile": "<PERSON><PERSON> <PERSON> liv<PERSON>", "summary": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "label": "Etiche<PERSON>", "rate": "<PERSON><PERSON>", "requiresShipping": "Necesită transport", "unitPrice": "Preț unitar", "startDate": "Data de începere", "endDate": "Data de încheiere", "draft": "Proiect", "values": "Valori"}, "quotes": {"domain": "Oferte", "title": "Oferte", "subtitle": "Gestionați ofertele și propunerile clienților", "noQuotes": "Nu s-au gă<PERSON>t oferte", "noQuotesDescription": "În prezent nu există oferte. Creați una din magazin.", "table": {"id": "ID Ofertă", "customer": "Client", "status": "Status", "company": "Companie", "amount": "Sumă", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Actualizat", "actions": "Acțiuni"}, "status": {"pending_merchant": "În așteptarea comerciantului", "pending_customer": "În așteptarea clientului", "merchant_rejected": "Respins de comerciant", "customer_rejected": "Respins de client", "accepted": "Acceptat", "unknown": "Necunoscut"}, "actions": {"sendQuote": "Trimite oferta", "rejectQuote": "Re<PERSON><PERSON> of<PERSON>a", "viewOrder": "<PERSON><PERSON><PERSON> comanda"}, "details": {"header": "<PERSON><PERSON>i ofert<PERSON>", "quoteSummary": "<PERSON><PERSON><PERSON>", "customer": "Client", "company": "Companie", "items": "Articole", "total": "Total", "subtotal": "Subtotal", "shipping": "Transport", "tax": "Taxă", "discounts": "<PERSON><PERSON><PERSON>", "originalTotal": "Total original", "quoteTotal": "Total ofertă", "messages": "Mesaje", "actions": "Acțiuni", "sendMessage": "Trimite mesaj", "send": "Trimite", "pickQuoteItem": "Alege articol ofertă", "selectQuoteItem": "Selectați un articol din ofertă pentru comentariu", "selectItem": "Selectează articol", "manage": "Gestionează", "phone": "Telefon", "spendingLimit": "Limită de cheltuieli", "name": "Nume", "manageQuote": "Gestionează oferta", "noItems": "Nu există articole în această ofertă", "noMessages": "Nu există mesaje pentru această ofertă"}, "items": {"title": "Produs", "quantity": "Cantitate", "unitPrice": "Preț unitar", "total": "Total"}, "messages": {"admin": "Administrator", "customer": "Client", "placeholder": "Scrieți mesajul aici..."}, "filters": {"status": "Filtrează după status"}, "confirmations": {"sendTitle": "Trimite oferta", "sendDescription": "Sunteți sigur că doriți să trimiteți această ofertă clientului?", "rejectTitle": "Re<PERSON><PERSON> of<PERSON>a", "rejectDescription": "Sunteți sigur că doriți să respingeți această ofertă?"}, "acceptance": {"message": "Oferta a fost acceptată"}, "toasts": {"sendSuccess": "Oferta a fost trimisă cu succes clientului", "sendError": "Trimiterea ofertei a eșuat", "rejectSuccess": "Oferta clientului a fost respinsă cu succes", "rejectError": "Respingerea ofertei a eșuat", "messageSuccess": "Mesajul a fost trimis cu succes clientului", "messageError": "Trimiterea mesajului a eșuat", "updateSuccess": "Oferta a fost actualizată cu succes"}, "manage": {"overridePriceHint": "Suprascrie prețul original pentru acest articol", "updatePrice": "Actualizează prețul"}}, "companies": {"domain": "Companii", "title": "Companii", "subtitle": "Gestionați relațiile de afaceri", "noCompanies": "Nu s-au găsit companii", "noCompaniesDescription": "Creați prima dvs. companie pentru a începe.", "notFound": "Compania nu a fost găsită", "table": {"name": "Nume", "phone": "Telefon", "email": "Email", "address": "Adresă", "employees": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Grup clien<PERSON>i", "actions": "Acțiuni"}, "fields": {"name": "Nume companie", "email": "Email", "phone": "Telefon", "website": "Site web", "address": "Adresă", "city": "Oraș", "state": "Stat", "zip": "Cod poștal", "zipCode": "Cod poștal", "country": "Țară", "currency": "Monedă", "logoUrl": "URL logo", "description": "Des<PERSON><PERSON><PERSON>", "employees": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Grup clien<PERSON>i", "approvalSettings": "<PERSON><PERSON><PERSON>"}, "placeholders": {"name": "Introduceți numele companiei", "email": "Introduceți adresa de email", "phone": "Introduceți numărul de telefon", "website": "Introduceți URL-ul site-ului web", "address": "Introduceți adresa", "city": "Introduceți orașul", "state": "Introduceți statul", "zip": "Introduceți codul poștal", "logoUrl": "Introduceți URL-ul logo-ului", "description": "Introduceți descrierea companiei", "selectCountry": "Selectați țara", "selectCurrency": "Selectați moneda"}, "validation": {"nameRequired": "Numele companiei este obligatoriu", "emailRequired": "Email-ul este obligatoriu", "emailInvalid": "Adresa de email nu este validă", "addressRequired": "Adresa este obligatorie", "cityRequired": "Orașul este obligatoriu", "stateRequired": "Statul este obligatoriu", "zipRequired": "Codul poștal este obligatoriu"}, "create": {"title": "Creează companie", "description": "Creați o nouă companie pentru a gestiona relațiile de afaceri.", "submit": "Creează companie"}, "edit": {"title": "Editează companie", "submit": "Actualizează companie"}, "details": {"actions": "Acțiuni"}, "approvals": {"requiresAdminApproval": "Necesită aprobare administrator", "requiresSalesManagerApproval": "Necesită aprobare manager <PERSON><PERSON><PERSON><PERSON><PERSON>", "noApprovalRequired": "Nu necesită aprobare"}, "deleteWarning": "Aceasta va șterge definitiv compania și toate datele asociate.", "approvalSettings": {"title": "<PERSON><PERSON><PERSON>", "requiresAdminApproval": "Necesită aprobare administrator", "requiresSalesManagerApproval": "Necesită aprobare manager <PERSON><PERSON><PERSON><PERSON><PERSON>", "requiresAdminApprovalDesc": "Comenzile de la această companie necesită aprobare administrator <PERSON><PERSON><PERSON> de procesare", "requiresSalesManagerApprovalDesc": "Comenzile de la această companie necesită aprobare manager v<PERSON><PERSON><PERSON><PERSON> de procesare", "updateSuccess": "Setările de aprobare au fost actualizate cu succes", "updateError": "Actualizarea setă<PERSON>or de aprobare a eșuat"}, "customerGroup": {"name": "Nume grup clienți", "description": "Gestionați grupurile de clienți pentru această companie", "noGroups": "Nu există grupuri de clienți disponibile", "addSuccess": "Compania a fost adăugată cu succes la grupul de clienți", "addError": "Adăugarea companiei la grupul de clienți a eșuat", "removeSuccess": "Compania a fost eliminată cu succes din grupul de clienți", "removeError": "Eliminarea companiei din grupul de clienți a eșuat"}, "actions": {"edit": "Editează companie", "editDetails": "Editează de<PERSON>ii", "manageCustomerGroup": "Gestionează grup clienți", "approvalSettings": "<PERSON><PERSON><PERSON>", "delete": "Șterge companie", "confirmDelete": "Confirmă ș<PERSON>ea"}, "delete": {"title": "Șterge companie", "description": "Sunteți sigur că doriți să ștergeți această companie? Această acțiune nu poate fi anulată."}, "employees": {"title": "<PERSON><PERSON><PERSON><PERSON>", "noEmployees": "Nu s-au găsit angajați pentru această companie", "name": "Nume", "email": "Email", "phone": "Telefon", "role": "Rol", "spendingLimit": "Limită ch<PERSON>i", "admin": "Administrator", "employee": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON> an<PERSON>", "create": {"title": "Creează angajat", "success": "Angajatul a fost creat cu succes", "error": "<PERSON><PERSON><PERSON> a eșuat"}, "form": {"details": "Informații detaliate", "permissions": "Per<PERSON><PERSON><PERSON>", "firstName": "Prenume", "lastName": "Nume de familie", "email": "Email", "phone": "Telefon", "spendingLimit": "Limită ch<PERSON>i", "adminAccess": "Acces administrator", "isAdmin": "Este administrator", "isAdminDesc": "Acordați privilegii de administrator acestui anga<PERSON>t", "isAdminTooltip": "Administratorii pot gestiona setările companiei și alți angajați", "firstNamePlaceholder": "Introduceți prenumele", "lastNamePlaceholder": "Introduceți numele de familie", "emailPlaceholder": "Introduceți adresa de email", "phonePlaceholder": "Introduceți numărul de telefon", "spendingLimitPlaceholder": "Introduceți limita de cheltuieli", "save": "Salvează", "saving": "Se salvează..."}, "delete": {"confirmation": "Sunteți sigur că doriți să ștergeți acest angajat?", "success": "Angajatul a fost șters cu succes"}, "edit": {"title": "Editează anga<PERSON>"}, "toasts": {"updateSuccess": "Angajatul a fost actualizat cu succes", "updateError": "Actualizarea angajatului a eșuat"}}, "toasts": {"createSuccess": "Compania a fost creată cu succes", "createError": "Crearea companiei a eșuat", "updateSuccess": "Compania a fost actualizată cu succes", "updateError": "Actualizarea companiei a eșuat", "deleteSuccess": "Compania a fost ștearsă cu succes", "deleteError": "Ștergerea companiei a eșuat"}}, "approvals": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Gestionați fluxurile de lucru de aprobare", "noApprovals": "Nu s-au gă<PERSON>t aprobări", "noApprovalsDescription": "În prezent nu există aprobări de revizuit.", "table": {"id": "ID", "type": "Tip", "company": "Companie", "customer": "Client", "amount": "Sumă", "status": "Status", "createdAt": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "În așteptare", "approved": "Aprobat", "rejected": "Respins", "expired": "Expirat", "unknown": "Necunoscut"}, "details": {"header": "Detalii aprobare", "summary": "<PERSON><PERSON><PERSON>", "company": "Companie", "customer": "Client", "order": "Comandă", "amount": "Sumă", "updatedAt": "Actualizat", "reason": "Motiv", "actions": "Acțiuni"}, "actions": {"approve": "Aprobă", "reject": "Respinge", "confirmApprove": "Confirmă aprobarea", "confirmReject": "Confirm<PERSON> respingerea", "reasonPlaceholder": "Introduceți motivul (opțional)..."}, "filters": {"status": "Filtrează după status"}, "toasts": {"approveSuccess": "Aprobat cu succes", "approveError": "Aprobarea a eșuat", "rejectSuccess": "Respins cu succes", "rejectError": "Respingerea a <PERSON>șuat"}}, "dateTime": {"years_one": "An", "years_other": "ani", "months_one": "Lună", "months_other": "<PERSON><PERSON>", "weeks_one": "Săptămână", "weeks_other": "Săptămâni", "days_one": "<PERSON><PERSON>", "days_other": "Zile", "hours_one": "Oră", "hours_other": "Ore", "minutes_one": "Minut", "minutes_other": "Minute", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "secunde"}}