import { LoaderFunctionArgs } from "react-router-dom"
import { sdk } from "../../../lib/client"
import { AdminQuoteResponse } from "../../../types"

export const quoteLoader = async ({ params }: LoaderFunctionArgs) => {
  const { id } = params

  if (!id) {
    throw new Response("Quote ID is required", { status: 400 })
  }

  try {
    const response = await sdk.client.fetch<AdminQuoteResponse>(`/admin/quotes/${id}`, {
      method: "GET",
    })
    
    return response
  } catch (error) {
    throw new Response("Quote not found", { status: 404 })
  }
}